# CMDB 平台配置文件示例
# 复制此文件为 config.yaml 并修改相应配置

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  mode: "debug"  # debug, release, test
  read_timeout: 30s
  write_timeout: 30s

# 数据库配置
database:
  postgres:
    host: "localhost"
    port: 5432
    user: "postgres"
    password: "password"
    dbname: "cmdb_platform"
    sslmode: "disable"
    timezone: "Asia/Shanghai"
    max_idle_conns: 10
    max_open_conns: 100
    conn_max_lifetime: 1h
  
  redis:
    host: "localhost"
    port: 6379
    password: ""
    db: 0
    pool_size: 10
    min_idle_conns: 5

# 日志配置
log:
  level: "info"  # debug, info, warn, error
  format: "json"  # json, text
  output: "stdout"  # stdout, file
  file_path: "logs/app.log"
  max_size: 100  # MB
  max_backups: 10
  max_age: 30  # days
  compress: true

# JWT 认证配置
jwt:
  secret: "your-super-secret-jwt-key-change-this-in-production"
  expire_hours: 24
  refresh_expire_hours: 168  # 7 days

# 云服务商配置
cloud_providers:
  # 阿里云配置
  aliyun:
    enabled: true
    access_key_id: "your-aliyun-access-key-id"
    access_key_secret: "your-aliyun-access-key-secret"
    region: "cn-hangzhou"
    endpoint: ""  # 可选，使用默认端点
  
  # 腾讯云配置
  tencent:
    enabled: true
    secret_id: "your-tencent-secret-id"
    secret_key: "your-tencent-secret-key"
    region: "ap-guangzhou"
    endpoint: ""  # 可选，使用默认端点
  
  # AWS 配置
  aws:
    enabled: true
    access_key_id: "your-aws-access-key-id"
    secret_access_key: "your-aws-secret-access-key"
    region: "us-east-1"
    endpoint: ""  # 可选，使用默认端点

# 同步配置
sync:
  enabled: true
  interval: "1h"  # 同步间隔：1m, 1h, 24h
  timeout: "10m"  # 同步超时时间
  retry_count: 3  # 重试次数
  retry_interval: "5m"  # 重试间隔
  
  # 并发控制
  max_concurrent_syncs: 3
  
  # 资源类型配置
  resource_types:
    - "ecs"      # 云服务器
    - "vpc"      # 虚拟私有云
    - "subnet"   # 子网
    - "slb"      # 负载均衡
    - "ebs"      # 云盘
    - "rds"      # 数据库
    - "sg"       # 安全组

# 告警配置
alert:
  enabled: true
  
  # 告警规则
  rules:
    # 资源状态告警
    resource_status:
      enabled: true
      check_interval: "5m"
      
    # 同步失败告警
    sync_failure:
      enabled: true
      threshold: 3  # 连续失败次数
      
    # 资源变更告警
    resource_change:
      enabled: true
      ignore_fields: ["last_sync_at", "updated_at"]
  
  # 通知配置
  notifications:
    # 邮件通知
    email:
      enabled: false
      smtp_host: "smtp.example.com"
      smtp_port: 587
      username: "<EMAIL>"
      password: "your-email-password"
      from: "CMDB Platform <<EMAIL>>"
      to: ["<EMAIL>"]
    
    # Webhook 通知
    webhook:
      enabled: false
      url: "https://your-webhook-url.com/alerts"
      timeout: "10s"
      retry_count: 3
    
    # 钉钉通知
    dingtalk:
      enabled: false
      webhook_url: "https://oapi.dingtalk.com/robot/send?access_token=your-token"
      secret: "your-dingtalk-secret"
    
    # 企业微信通知
    wechat:
      enabled: false
      webhook_url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key"

# CORS 配置
cors:
  allow_origins: ["*"]
  allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  allow_headers: ["*"]
  expose_headers: ["*"]
  allow_credentials: true
  max_age: 86400

# 限流配置
rate_limit:
  enabled: true
  requests_per_minute: 100
  burst: 200

# 缓存配置
cache:
  enabled: true
  default_ttl: "1h"
  
  # 具体缓存配置
  resource_stats: "5m"
  user_profile: "30m"
  cloud_regions: "24h"

# 监控配置
monitoring:
  enabled: true
  
  # Prometheus 指标
  prometheus:
    enabled: true
    path: "/metrics"
    
  # 健康检查
  health:
    enabled: true
    path: "/health"
    
  # 性能分析
  pprof:
    enabled: false  # 仅在开发环境启用
    path: "/debug/pprof"

# 安全配置
security:
  # 密码策略
  password:
    min_length: 8
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_symbols: true
  
  # 会话配置
  session:
    timeout: "24h"
    max_sessions_per_user: 5
    
  # API 安全
  api:
    enable_https: false  # 生产环境建议启用
    tls_cert_file: ""
    tls_key_file: ""

# 功能开关
features:
  # 资源拓扑图
  topology_view: true
  
  # 成本分析
  cost_analysis: false
  
  # 自动化运维
  automation: false
  
  # 审计日志
  audit_log: true
  
  # 数据导出
  data_export: true

# 第三方集成
integrations:
  # Grafana 集成
  grafana:
    enabled: false
    url: "http://grafana.example.com"
    api_key: "your-grafana-api-key"
  
  # Prometheus 集成
  prometheus:
    enabled: false
    url: "http://prometheus.example.com"
  
  # LDAP 集成
  ldap:
    enabled: false
    server: "ldap.example.com"
    port: 389
    bind_dn: "cn=admin,dc=example,dc=com"
    bind_password: "admin-password"
    base_dn: "dc=example,dc=com"
    user_filter: "(uid=%s)"

# 开发配置
development:
  # 调试模式
  debug: true
  
  # 热重载
  hot_reload: true
  
  # 模拟数据
  mock_data: false
  
  # SQL 日志
  sql_debug: false
