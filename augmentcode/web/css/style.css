/* CMDB 平台样式 */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* 统计卡片 */
.stat-card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stat-card.success {
    background: linear-gradient(135deg, var(--success-color), #146c43);
}

.stat-card.warning {
    background: linear-gradient(135deg, var(--warning-color), #e6a700);
}

.stat-card.danger {
    background: linear-gradient(135deg, var(--danger-color), #b02a37);
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

.stat-card .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 表格样式 */
.table {
    background-color: white;
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid var(--primary-color);
    font-weight: 600;
    color: var(--dark-color);
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* 状态标签 */
.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-running {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-stopped {
    background-color: #f8d7da;
    color: #721c24;
}

.status-starting {
    background-color: #fff3cd;
    color: #856404;
}

.status-unknown {
    background-color: #e2e3e5;
    color: #41464b;
}

/* 告警级别样式 */
.alert-level-info {
    color: var(--info-color);
}

.alert-level-warning {
    color: var(--warning-color);
}

.alert-level-critical {
    color: var(--danger-color);
}

/* 按钮样式 */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

/* 搜索框样式 */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-left: 2.5rem;
    border-radius: 0.5rem;
}

.search-box .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
}

/* 分页样式 */
.pagination {
    margin-bottom: 0;
}

.page-link {
    border-radius: 0.375rem;
    margin: 0 0.125rem;
    border: 1px solid #dee2e6;
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 模态框样式 */
.modal-content {
    border-radius: 0.75rem;
    border: none;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.75rem 0.75rem 0 0;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0 0 0.75rem 0.75rem;
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 0.125rem solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--secondary-color);
}

.empty-state .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state .empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.empty-state .empty-description {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 0.5rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .stat-card {
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .btn-group-vertical .btn {
        margin-bottom: 0.25rem;
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: 0.375rem;
}

/* 进度条样式 */
.progress {
    height: 0.5rem;
    border-radius: 0.25rem;
}

.progress-bar {
    border-radius: 0.25rem;
}

/* 标签样式 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
}

/* 面包屑导航 */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: var(--secondary-color);
}

/* 侧边栏样式（如果需要） */
.sidebar {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    height: fit-content;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.sidebar .nav-link {
    color: var(--dark-color);
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    margin-bottom: 0.25rem;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}
