// 主应用程序
class App {
    constructor() {
        this.currentPage = 'dashboard';
        this.user = null;
        this.init();
    }

    async init() {
        // 检查认证状态
        if (api.token) {
            try {
                const response = await api.getProfile();
                this.user = response.data;
                this.showMainApp();
                this.loadPage(this.currentPage);
            } catch (error) {
                console.error('获取用户信息失败:', error);
                this.showLogin();
            }
        } else {
            this.showLogin();
        }

        // 绑定导航事件
        this.bindNavigation();
        
        // 定期检查健康状态
        this.startHealthCheck();
    }

    showLogin() {
        const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
        loginModal.show();
    }

    showMainApp() {
        const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
        if (loginModal) {
            loginModal.hide();
        }
        
        // 更新用户信息显示
        if (this.user) {
            document.getElementById('userDropdown').innerHTML = `
                <i class="bi bi-person-circle"></i> ${this.user.display_name || this.user.username}
            `;
        }
    }

    bindNavigation() {
        // 导航链接点击事件
        document.querySelectorAll('[data-page]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = e.target.closest('[data-page]').dataset.page;
                this.navigateTo(page);
            });
        });

        // 浏览器后退/前进事件
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.page) {
                this.loadPage(e.state.page);
            }
        });
    }

    navigateTo(page) {
        this.currentPage = page;
        this.loadPage(page);
        
        // 更新URL
        const url = page === 'dashboard' ? '/' : `/#${page}`;
        history.pushState({ page }, '', url);
        
        // 更新导航状态
        this.updateNavigation(page);
    }

    updateNavigation(page) {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[data-page="${page}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    async loadPage(page) {
        const content = document.getElementById('content');
        
        try {
            showLoading();
            
            switch (page) {
                case 'dashboard':
                    await this.loadDashboard();
                    break;
                case 'resources':
                    await this.loadResources();
                    break;
                case 'cloud-accounts':
                    await this.loadCloudAccounts();
                    break;
                case 'alerts':
                    await this.loadAlerts();
                    break;
                case 'sync-tasks':
                    await this.loadSyncTasks();
                    break;
                default:
                    content.innerHTML = '<div class="alert alert-warning">页面不存在</div>';
            }
        } catch (error) {
            console.error('加载页面失败:', error);
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    加载页面失败: ${error.message}
                </div>
            `;
        } finally {
            hideLoading();
        }
    }

    async loadDashboard() {
        const dashboard = new Dashboard();
        await dashboard.render();
    }

    async loadResources() {
        const resources = new Resources();
        await resources.render();
    }

    async loadCloudAccounts() {
        const cloudAccounts = new CloudAccounts();
        await cloudAccounts.render();
    }

    async loadAlerts() {
        const alerts = new Alerts();
        await alerts.render();
    }

    async loadSyncTasks() {
        const syncTasks = new SyncTasks();
        await syncTasks.render();
    }

    startHealthCheck() {
        // 每30秒检查一次健康状态
        setInterval(async () => {
            try {
                await api.getHealth();
            } catch (error) {
                console.warn('健康检查失败:', error);
                // 可以在这里显示连接状态指示器
            }
        }, 30000);
    }
}

// 全局函数
async function login() {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    
    if (!username || !password) {
        showToast('请输入用户名和密码', 'warning');
        return;
    }
    
    try {
        showLoading();
        const response = await api.login(username, password);
        
        if (response.data) {
            app.user = response.data.user;
            app.showMainApp();
            app.loadPage('dashboard');
            showToast('登录成功', 'success');
        }
    } catch (error) {
        console.error('登录失败:', error);
        showToast('登录失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

async function logout() {
    try {
        await api.logout();
        app.user = null;
        app.showLogin();
        showToast('已退出登录', 'info');
    } catch (error) {
        console.error('退出登录失败:', error);
        // 即使API调用失败，也要清除本地状态
        api.setToken(null);
        app.user = null;
        app.showLogin();
    }
}

function showLoginModal() {
    const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
    loginModal.show();
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();
    
    // 绑定登录表单回车事件
    document.getElementById('loginForm').addEventListener('submit', (e) => {
        e.preventDefault();
        login();
    });
    
    // 绑定登录表单回车键
    document.getElementById('password').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            login();
        }
    });
});

// 全局错误处理
window.addEventListener('error', (e) => {
    console.error('全局错误:', e.error);
    showToast('发生未知错误，请刷新页面重试', 'error');
});

window.addEventListener('unhandledrejection', (e) => {
    console.error('未处理的Promise拒绝:', e.reason);
    showToast('操作失败，请重试', 'error');
});

// 工具函数
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

function formatNumber(num) {
    return new Intl.NumberFormat('zh-CN').format(num);
}

function truncateText(text, maxLength = 50) {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
