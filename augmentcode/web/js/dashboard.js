// 仪表板页面
class Dashboard {
    constructor() {
        this.stats = null;
        this.charts = {};
    }

    async render() {
        const content = document.getElementById('content');
        
        content.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <h2><i class="bi bi-speedometer2"></i> 仪表板</h2>
                    <p class="text-muted">云资源概览和统计信息</p>
                </div>
            </div>
            
            <!-- 统计卡片 -->
            <div class="row" id="statsCards">
                <!-- 统计卡片将在这里动态生成 -->
            </div>
            
            <!-- 图表区域 -->
            <div class="row mt-4">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-pie-chart"></i> 资源类型分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="resourceTypeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-bar-chart"></i> 云服务商分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="providerChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-activity"></i> 资源状态分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="statusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-exclamation-triangle"></i> 告警统计</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="alertChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 最近活动 -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-clock-history"></i> 最近活动</h5>
                        </div>
                        <div class="card-body">
                            <div id="recentActivities">
                                <!-- 最近活动列表 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        await this.loadData();
        this.renderStatsCards();
        this.renderCharts();
        this.loadRecentActivities();
    }

    async loadData() {
        try {
            const response = await api.getResourceStats();
            this.stats = response.data;
        } catch (error) {
            console.error('加载统计数据失败:', error);
            showToast('加载统计数据失败', 'error');
            this.stats = {
                total_resources: 0,
                resources_by_type: {},
                resources_by_cloud: {},
                resources_by_status: {},
                alerts_count: 0,
                active_alerts: 0
            };
        }
    }

    renderStatsCards() {
        const statsCards = document.getElementById('statsCards');
        
        const cards = [
            {
                title: '总资源数',
                value: this.stats.total_resources || 0,
                icon: 'server',
                color: 'primary'
            },
            {
                title: '运行中资源',
                value: this.stats.resources_by_status?.running || 0,
                icon: 'play-circle',
                color: 'success'
            },
            {
                title: '活跃告警',
                value: this.stats.active_alerts || 0,
                icon: 'exclamation-triangle',
                color: 'warning'
            },
            {
                title: '总告警数',
                value: this.stats.alerts_count || 0,
                icon: 'bell',
                color: 'danger'
            }
        ];
        
        statsCards.innerHTML = cards.map(card => `
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card ${card.color}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number">${formatNumber(card.value)}</div>
                            <div class="stat-label">${card.title}</div>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-${card.icon}"></i>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    renderCharts() {
        this.renderResourceTypeChart();
        this.renderProviderChart();
        this.renderStatusChart();
        this.renderAlertChart();
    }

    renderResourceTypeChart() {
        const ctx = document.getElementById('resourceTypeChart').getContext('2d');
        const data = this.stats.resources_by_type || {};
        
        this.charts.resourceType = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: Object.keys(data).map(key => this.getResourceTypeLabel(key)),
                datasets: [{
                    data: Object.values(data),
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF',
                        '#FF9F40',
                        '#FF6384',
                        '#C9CBCF'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    renderProviderChart() {
        const ctx = document.getElementById('providerChart').getContext('2d');
        const data = this.stats.resources_by_cloud || {};
        
        this.charts.provider = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: Object.keys(data).map(key => this.getProviderLabel(key)),
                datasets: [{
                    label: '资源数量',
                    data: Object.values(data),
                    backgroundColor: '#36A2EB'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    renderStatusChart() {
        const ctx = document.getElementById('statusChart').getContext('2d');
        const data = this.stats.resources_by_status || {};
        
        this.charts.status = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: Object.keys(data).map(key => this.getStatusLabel(key)),
                datasets: [{
                    data: Object.values(data),
                    backgroundColor: [
                        '#4BC0C0', // running
                        '#FF6384', // stopped
                        '#FFCE56', // starting
                        '#FF9F40', // stopping
                        '#C9CBCF'  // unknown
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    renderAlertChart() {
        const ctx = document.getElementById('alertChart').getContext('2d');
        
        // 模拟告警级别数据
        const alertData = {
            info: Math.floor(this.stats.alerts_count * 0.5),
            warning: Math.floor(this.stats.alerts_count * 0.3),
            critical: Math.floor(this.stats.alerts_count * 0.2)
        };
        
        this.charts.alert = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['信息', '警告', '严重'],
                datasets: [{
                    data: Object.values(alertData),
                    backgroundColor: [
                        '#36A2EB', // info
                        '#FFCE56', // warning
                        '#FF6384'  // critical
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    async loadRecentActivities() {
        const container = document.getElementById('recentActivities');
        
        try {
            // 这里应该调用获取最近活动的API
            // 暂时使用模拟数据
            const activities = [
                {
                    type: 'resource_created',
                    message: '创建了新的ECS实例 web-server-01',
                    time: new Date(Date.now() - 5 * 60 * 1000),
                    icon: 'plus-circle',
                    color: 'success'
                },
                {
                    type: 'alert_triggered',
                    message: '触发告警：数据库连接异常',
                    time: new Date(Date.now() - 15 * 60 * 1000),
                    icon: 'exclamation-triangle',
                    color: 'warning'
                },
                {
                    type: 'sync_completed',
                    message: '阿里云账号同步完成',
                    time: new Date(Date.now() - 30 * 60 * 1000),
                    icon: 'arrow-repeat',
                    color: 'info'
                },
                {
                    type: 'resource_updated',
                    message: '更新了负载均衡器配置',
                    time: new Date(Date.now() - 45 * 60 * 1000),
                    icon: 'pencil-square',
                    color: 'primary'
                }
            ];
            
            if (activities.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="bi bi-clock-history"></i>
                        </div>
                        <div class="empty-title">暂无活动记录</div>
                        <div class="empty-description">最近没有资源变更或系统活动</div>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = `
                <div class="list-group list-group-flush">
                    ${activities.map(activity => `
                        <div class="list-group-item d-flex align-items-center">
                            <div class="me-3">
                                <i class="bi bi-${activity.icon} text-${activity.color}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-medium">${activity.message}</div>
                                <small class="text-muted">${this.formatTimeAgo(activity.time)}</small>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        } catch (error) {
            console.error('加载最近活动失败:', error);
            container.innerHTML = `
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    加载最近活动失败
                </div>
            `;
        }
    }

    getResourceTypeLabel(type) {
        const labels = {
            ecs: 'ECS实例',
            vpc: 'VPC网络',
            subnet: '子网',
            slb: '负载均衡',
            ebs: '云盘',
            rds: 'RDS数据库',
            redis: 'Redis缓存',
            sg: '安全组'
        };
        return labels[type] || type.toUpperCase();
    }

    getProviderLabel(provider) {
        const labels = {
            aliyun: '阿里云',
            tencent: '腾讯云',
            aws: 'AWS'
        };
        return labels[provider] || provider;
    }

    getStatusLabel(status) {
        const labels = {
            running: '运行中',
            stopped: '已停止',
            starting: '启动中',
            stopping: '停止中',
            unknown: '未知'
        };
        return labels[status] || status;
    }

    formatTimeAgo(date) {
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        return `${days}天前`;
    }

    destroy() {
        // 销毁图表实例
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
        this.charts = {};
    }
}
