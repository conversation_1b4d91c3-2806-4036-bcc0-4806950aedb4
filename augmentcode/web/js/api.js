// API 客户端
class APIClient {
    constructor() {
        this.baseURL = '/api/v1';
        this.token = localStorage.getItem('access_token');
    }

    // 设置认证令牌
    setToken(token) {
        this.token = token;
        if (token) {
            localStorage.setItem('access_token', token);
        } else {
            localStorage.removeItem('access_token');
        }
    }

    // 获取请求头
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json',
        };
        
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }
        
        return headers;
    }

    // 通用请求方法
    async request(method, url, data = null) {
        const config = {
            method,
            headers: this.getHeaders(),
        };

        if (data) {
            config.body = JSON.stringify(data);
        }

        try {
            const response = await fetch(`${this.baseURL}${url}`, config);
            
            if (response.status === 401) {
                // 令牌过期，跳转到登录页
                this.setToken(null);
                showLoginModal();
                throw new Error('认证失败，请重新登录');
            }

            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.message || '请求失败');
            }

            return result;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    // GET 请求
    async get(url) {
        return this.request('GET', url);
    }

    // POST 请求
    async post(url, data) {
        return this.request('POST', url, data);
    }

    // PUT 请求
    async put(url, data) {
        return this.request('PUT', url, data);
    }

    // DELETE 请求
    async delete(url) {
        return this.request('DELETE', url);
    }

    // 认证相关API
    async login(username, password) {
        const response = await this.request('POST', '/auth/login', {
            username,
            password
        });
        
        if (response.data && response.data.token) {
            this.setToken(response.data.token);
        }
        
        return response;
    }

    async logout() {
        try {
            await this.request('POST', '/auth/logout');
        } finally {
            this.setToken(null);
        }
    }

    async getProfile() {
        return this.get('/auth/profile');
    }

    async changePassword(oldPassword, newPassword) {
        return this.post('/auth/change-password', {
            old_password: oldPassword,
            new_password: newPassword
        });
    }

    // 资源相关API
    async getResources(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.get(`/resources${queryString ? '?' + queryString : ''}`);
    }

    async getResource(id) {
        return this.get(`/resources/${id}`);
    }

    async createResource(data) {
        return this.post('/resources', data);
    }

    async updateResource(id, data) {
        return this.put(`/resources/${id}`, data);
    }

    async deleteResource(id) {
        return this.delete(`/resources/${id}`);
    }

    async syncResources(cloudAccountId, resourceTypes = []) {
        return this.post('/resources/sync', {
            cloud_account_id: cloudAccountId,
            type: 'manual',
            resource_types: resourceTypes
        });
    }

    async getResourceStats() {
        return this.get('/resources/stats');
    }

    // 云账号相关API
    async getCloudAccounts() {
        return this.get('/cloud-accounts');
    }

    async getCloudAccount(id) {
        return this.get(`/cloud-accounts/${id}`);
    }

    async createCloudAccount(data) {
        return this.post('/cloud-accounts', data);
    }

    async updateCloudAccount(id, data) {
        return this.put(`/cloud-accounts/${id}`, data);
    }

    async deleteCloudAccount(id) {
        return this.delete(`/cloud-accounts/${id}`);
    }

    // 告警相关API
    async getAlerts(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.get(`/alerts${queryString ? '?' + queryString : ''}`);
    }

    async getAlert(id) {
        return this.get(`/alerts/${id}`);
    }

    async createAlert(data) {
        return this.post('/alerts', data);
    }

    async updateAlert(id, data) {
        return this.put(`/alerts/${id}`, data);
    }

    async resolveAlert(id) {
        return this.put(`/alerts/${id}`, { status: 'resolved' });
    }

    async deleteAlert(id) {
        return this.delete(`/alerts/${id}`);
    }

    // 同步任务相关API
    async getSyncTasks(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.get(`/sync-tasks${queryString ? '?' + queryString : ''}`);
    }

    async getSyncTask(id) {
        return this.get(`/sync-tasks/${id}`);
    }

    // 用户相关API
    async getUsers(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.get(`/users${queryString ? '?' + queryString : ''}`);
    }

    async getUser(id) {
        return this.get(`/users/${id}`);
    }

    async createUser(data) {
        return this.post('/users', data);
    }

    async updateUser(id, data) {
        return this.put(`/users/${id}`, data);
    }

    async deleteUser(id) {
        return this.delete(`/users/${id}`);
    }

    // 健康检查
    async getHealth() {
        return fetch('/health').then(res => res.json());
    }
}

// 创建全局API客户端实例
const api = new APIClient();

// 工具函数
function showLoading() {
    document.getElementById('loadingIndicator').classList.remove('d-none');
}

function hideLoading() {
    document.getElementById('loadingIndicator').classList.add('d-none');
}

function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toastContainer');
    const toastId = 'toast-' + Date.now();
    
    const toastHTML = `
        <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="bi bi-${getToastIcon(type)} text-${type} me-2"></i>
                <strong class="me-auto">${getToastTitle(type)}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHTML);
    
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement);
    toast.show();
    
    // 自动移除
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

function getToastIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

function getToastTitle(type) {
    const titles = {
        success: '成功',
        error: '错误',
        warning: '警告',
        info: '信息'
    };
    return titles[type] || '信息';
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 获取状态样式类
function getStatusClass(status) {
    const statusClasses = {
        running: 'status-running',
        stopped: 'status-stopped',
        starting: 'status-starting',
        stopping: 'status-stopping',
        unknown: 'status-unknown'
    };
    return statusClasses[status] || 'status-unknown';
}

// 获取告警级别样式类
function getAlertLevelClass(level) {
    const levelClasses = {
        info: 'alert-level-info',
        warning: 'alert-level-warning',
        critical: 'alert-level-critical'
    };
    return levelClasses[level] || 'alert-level-info';
}

// 获取云服务商图标
function getProviderIcon(provider) {
    const icons = {
        aliyun: 'cloud',
        tencent: 'cloud',
        aws: 'cloud'
    };
    return icons[provider] || 'cloud';
}

// 获取资源类型图标
function getResourceTypeIcon(type) {
    const icons = {
        ecs: 'server',
        vpc: 'diagram-3',
        subnet: 'diagram-2',
        slb: 'distribute-horizontal',
        ebs: 'hdd',
        rds: 'database',
        redis: 'database-gear',
        sg: 'shield-check'
    };
    return icons[type] || 'box';
}
