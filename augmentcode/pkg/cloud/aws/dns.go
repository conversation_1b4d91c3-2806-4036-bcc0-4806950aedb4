package aws

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"cmdb-platform/pkg/cloud"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/route53"
)

// DNSClient AWS Route53 DNS客户端
type DNSClient struct {
	client *route53.Route53
}

// NewDNSClient 创建AWS DNS客户端
func NewDNSClient(accessKeyID, secretAccessKey, region string) (*DNSClient, error) {
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(region),
		Credentials: credentials.NewStaticCredentials(
			accessKeyID,
			secretAccessKey,
			"",
		),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create aws session: %w", err)
	}

	client := route53.New(sess)

	return &DNSClient{
		client: client,
	}, nil
}

// ListDNSZones 列出DNS域名
func (d *DNSClient) ListDNSZones(ctx context.Context) ([]cloud.DNSZone, error) {
	var allZones []cloud.DNSZone
	var nextMarker *string

	for {
		input := &route53.ListHostedZonesInput{
			MaxItems: aws.String("100"),
		}
		if nextMarker != nil {
			input.Marker = nextMarker
		}

		result, err := d.client.ListHostedZonesWithContext(ctx, input)
		if err != nil {
			return nil, fmt.Errorf("failed to list hosted zones: %w", err)
		}

		for _, zone := range result.HostedZones {
			dnsZone := cloud.DNSZone{
				ID:          *zone.Id,
				DomainName:  *zone.Name,
				Status:      "active",
				TTL:         300, // 默认TTL
				Description: "",
				Tags:        make(map[string]string),
				CreatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
				UpdatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
			}

			if zone.Config != nil && zone.Config.Comment != nil {
				dnsZone.Description = *zone.Config.Comment
			}

			allZones = append(allZones, dnsZone)
		}

		if !*result.IsTruncated {
			break
		}
		nextMarker = result.NextMarker
	}

	return allZones, nil
}

// GetDNSZone 获取DNS域名详情
func (d *DNSClient) GetDNSZone(ctx context.Context, zoneID string) (*cloud.DNSZone, error) {
	input := &route53.GetHostedZoneInput{
		Id: aws.String(zoneID),
	}

	result, err := d.client.GetHostedZoneWithContext(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to get hosted zone: %w", err)
	}

	zone := &cloud.DNSZone{
		ID:          *result.HostedZone.Id,
		DomainName:  *result.HostedZone.Name,
		Status:      "active",
		TTL:         300,
		Description: "",
		Tags:        make(map[string]string),
		CreatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
		UpdatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
	}

	if result.HostedZone.Config != nil && result.HostedZone.Config.Comment != nil {
		zone.Description = *result.HostedZone.Config.Comment
	}

	return zone, nil
}

// CreateDNSZone 创建DNS域名
func (d *DNSClient) CreateDNSZone(ctx context.Context, zone *cloud.DNSZone) (*cloud.DNSZone, error) {
	input := &route53.CreateHostedZoneInput{
		Name:            aws.String(zone.DomainName),
		CallerReference: aws.String(fmt.Sprintf("%s-%d", zone.DomainName, time.Now().Unix())),
	}

	if zone.Description != "" {
		input.HostedZoneConfig = &route53.HostedZoneConfig{
			Comment: aws.String(zone.Description),
		}
	}

	result, err := d.client.CreateHostedZoneWithContext(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to create hosted zone: %w", err)
	}

	createdZone := &cloud.DNSZone{
		ID:          *result.HostedZone.Id,
		DomainName:  *result.HostedZone.Name,
		Status:      "active",
		TTL:         zone.TTL,
		Description: zone.Description,
		Tags:        zone.Tags,
		CreatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
		UpdatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
	}

	return createdZone, nil
}

// UpdateDNSZone 更新DNS域名
func (d *DNSClient) UpdateDNSZone(ctx context.Context, zoneID string, zone *cloud.DNSZone) (*cloud.DNSZone, error) {
	// Route53只支持更新域名的注释
	input := &route53.UpdateHostedZoneCommentInput{
		Id:      aws.String(zoneID),
		Comment: aws.String(zone.Description),
	}

	_, err := d.client.UpdateHostedZoneCommentWithContext(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to update hosted zone: %w", err)
	}

	// 返回更新后的域名信息
	return d.GetDNSZone(ctx, zoneID)
}

// DeleteDNSZone 删除DNS域名
func (d *DNSClient) DeleteDNSZone(ctx context.Context, zoneID string) error {
	input := &route53.DeleteHostedZoneInput{
		Id: aws.String(zoneID),
	}

	_, err := d.client.DeleteHostedZoneWithContext(ctx, input)
	if err != nil {
		return fmt.Errorf("failed to delete hosted zone: %w", err)
	}

	return nil
}

// ListDNSRecords 列出DNS记录
func (d *DNSClient) ListDNSRecords(ctx context.Context, zoneID string) ([]cloud.DNSRecord, error) {
	var allRecords []cloud.DNSRecord
	var nextName, nextType *string

	for {
		input := &route53.ListResourceRecordSetsInput{
			HostedZoneId: aws.String(zoneID),
			MaxItems:     aws.String("300"),
		}
		if nextName != nil {
			input.StartRecordName = nextName
		}
		if nextType != nil {
			input.StartRecordType = nextType
		}

		result, err := d.client.ListResourceRecordSetsWithContext(ctx, input)
		if err != nil {
			return nil, fmt.Errorf("failed to list resource record sets: %w", err)
		}

		for _, recordSet := range result.ResourceRecordSets {
			// 跳过NS和SOA记录（系统记录）
			if *recordSet.Type == "NS" || *recordSet.Type == "SOA" {
				continue
			}

			var values []string
			for _, record := range recordSet.ResourceRecords {
				values = append(values, *record.Value)
			}

			dnsRecord := cloud.DNSRecord{
				ID:          fmt.Sprintf("%s-%s-%s", zoneID, *recordSet.Name, *recordSet.Type),
				ZoneID:      zoneID,
				Name:        *recordSet.Name,
				Type:        *recordSet.Type,
				Value:       values[0], // 取第一个值
				TTL:         int(*recordSet.TTL),
				Status:      "active",
				Description: "",
				Tags:        make(map[string]string),
				CreatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
				UpdatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
			}

			// 处理MX记录的优先级
			if *recordSet.Type == "MX" && len(values) > 0 {
				// MX记录格式: "10 mail.example.com"
				parts := strings.Fields(values[0])
				if len(parts) >= 2 {
					if priority, err := strconv.Atoi(parts[0]); err == nil {
						dnsRecord.Priority = &priority
						dnsRecord.Value = strings.Join(parts[1:], " ")
					}
				}
			}

			allRecords = append(allRecords, dnsRecord)
		}

		if !*result.IsTruncated {
			break
		}
		nextName = result.NextRecordName
		nextType = result.NextRecordType
	}

	return allRecords, nil
}

// GetDNSRecord 获取DNS记录详情
func (d *DNSClient) GetDNSRecord(ctx context.Context, zoneID, recordID string) (*cloud.DNSRecord, error) {
	// Route53没有直接获取单个记录的API，需要通过列表查找
	records, err := d.ListDNSRecords(ctx, zoneID)
	if err != nil {
		return nil, err
	}

	for _, record := range records {
		if record.ID == recordID {
			return &record, nil
		}
	}

	return nil, fmt.Errorf("record not found: %s", recordID)
}

// CreateDNSRecord 创建DNS记录
func (d *DNSClient) CreateDNSRecord(ctx context.Context, zoneID string, record *cloud.DNSRecord) (*cloud.DNSRecord, error) {
	var resourceRecords []*route53.ResourceRecord

	// 处理MX记录的特殊格式
	if record.Type == "MX" && record.Priority != nil {
		value := fmt.Sprintf("%d %s", *record.Priority, record.Value)
		resourceRecords = append(resourceRecords, &route53.ResourceRecord{
			Value: aws.String(value),
		})
	} else {
		resourceRecords = append(resourceRecords, &route53.ResourceRecord{
			Value: aws.String(record.Value),
		})
	}

	change := &route53.Change{
		Action: aws.String("CREATE"),
		ResourceRecordSet: &route53.ResourceRecordSet{
			Name:            aws.String(record.Name),
			Type:            aws.String(record.Type),
			TTL:             aws.Int64(int64(record.TTL)),
			ResourceRecords: resourceRecords,
		},
	}

	input := &route53.ChangeResourceRecordSetsInput{
		HostedZoneId: aws.String(zoneID),
		ChangeBatch: &route53.ChangeBatch{
			Changes: []*route53.Change{change},
		},
	}

	_, err := d.client.ChangeResourceRecordSetsWithContext(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to create dns record: %w", err)
	}

	// 返回创建的记录信息
	createdRecord := &cloud.DNSRecord{
		ID:          fmt.Sprintf("%s-%s-%s", zoneID, record.Name, record.Type),
		ZoneID:      zoneID,
		Name:        record.Name,
		Type:        record.Type,
		Value:       record.Value,
		TTL:         record.TTL,
		Priority:    record.Priority,
		Status:      "active",
		Description: record.Description,
		Tags:        record.Tags,
		CreatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
		UpdatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
	}

	return createdRecord, nil
}

// UpdateDNSRecord 更新DNS记录
func (d *DNSClient) UpdateDNSRecord(ctx context.Context, zoneID, recordID string, record *cloud.DNSRecord) (*cloud.DNSRecord, error) {
	// Route53需要先删除再创建来实现更新
	var resourceRecords []*route53.ResourceRecord

	if record.Type == "MX" && record.Priority != nil {
		value := fmt.Sprintf("%d %s", *record.Priority, record.Value)
		resourceRecords = append(resourceRecords, &route53.ResourceRecord{
			Value: aws.String(value),
		})
	} else {
		resourceRecords = append(resourceRecords, &route53.ResourceRecord{
			Value: aws.String(record.Value),
		})
	}

	change := &route53.Change{
		Action: aws.String("UPSERT"), // UPSERT会自动处理更新或创建
		ResourceRecordSet: &route53.ResourceRecordSet{
			Name:            aws.String(record.Name),
			Type:            aws.String(record.Type),
			TTL:             aws.Int64(int64(record.TTL)),
			ResourceRecords: resourceRecords,
		},
	}

	input := &route53.ChangeResourceRecordSetsInput{
		HostedZoneId: aws.String(zoneID),
		ChangeBatch: &route53.ChangeBatch{
			Changes: []*route53.Change{change},
		},
	}

	_, err := d.client.ChangeResourceRecordSetsWithContext(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to update dns record: %w", err)
	}

	return d.GetDNSRecord(ctx, zoneID, recordID)
}

// DeleteDNSRecord 删除DNS记录
func (d *DNSClient) DeleteDNSRecord(ctx context.Context, zoneID, recordID string) error {
	// 先获取记录信息
	record, err := d.GetDNSRecord(ctx, zoneID, recordID)
	if err != nil {
		return err
	}

	var resourceRecords []*route53.ResourceRecord
	if record.Type == "MX" && record.Priority != nil {
		value := fmt.Sprintf("%d %s", *record.Priority, record.Value)
		resourceRecords = append(resourceRecords, &route53.ResourceRecord{
			Value: aws.String(value),
		})
	} else {
		resourceRecords = append(resourceRecords, &route53.ResourceRecord{
			Value: aws.String(record.Value),
		})
	}

	change := &route53.Change{
		Action: aws.String("DELETE"),
		ResourceRecordSet: &route53.ResourceRecordSet{
			Name:            aws.String(record.Name),
			Type:            aws.String(record.Type),
			TTL:             aws.Int64(int64(record.TTL)),
			ResourceRecords: resourceRecords,
		},
	}

	input := &route53.ChangeResourceRecordSetsInput{
		HostedZoneId: aws.String(zoneID),
		ChangeBatch: &route53.ChangeBatch{
			Changes: []*route53.Change{change},
		},
	}

	_, err = d.client.ChangeResourceRecordSetsWithContext(ctx, input)
	if err != nil {
		return fmt.Errorf("failed to delete dns record: %w", err)
	}

	return nil
}

// BatchCreateDNSRecords 批量创建DNS记录
func (d *DNSClient) BatchCreateDNSRecords(ctx context.Context, zoneID string, records []*cloud.DNSRecord) ([]*cloud.DNSRecord, error) {
	var changes []*route53.Change
	var createdRecords []*cloud.DNSRecord

	for _, record := range records {
		var resourceRecords []*route53.ResourceRecord

		if record.Type == "MX" && record.Priority != nil {
			value := fmt.Sprintf("%d %s", *record.Priority, record.Value)
			resourceRecords = append(resourceRecords, &route53.ResourceRecord{
				Value: aws.String(value),
			})
		} else {
			resourceRecords = append(resourceRecords, &route53.ResourceRecord{
				Value: aws.String(record.Value),
			})
		}

		change := &route53.Change{
			Action: aws.String("CREATE"),
			ResourceRecordSet: &route53.ResourceRecordSet{
				Name:            aws.String(record.Name),
				Type:            aws.String(record.Type),
				TTL:             aws.Int64(int64(record.TTL)),
				ResourceRecords: resourceRecords,
			},
		}
		changes = append(changes, change)

		createdRecord := &cloud.DNSRecord{
			ID:          fmt.Sprintf("%s-%s-%s", zoneID, record.Name, record.Type),
			ZoneID:      zoneID,
			Name:        record.Name,
			Type:        record.Type,
			Value:       record.Value,
			TTL:         record.TTL,
			Priority:    record.Priority,
			Status:      "active",
			Description: record.Description,
			Tags:        record.Tags,
			CreatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
			UpdatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
		}
		createdRecords = append(createdRecords, createdRecord)
	}

	input := &route53.ChangeResourceRecordSetsInput{
		HostedZoneId: aws.String(zoneID),
		ChangeBatch: &route53.ChangeBatch{
			Changes: changes,
		},
	}

	_, err := d.client.ChangeResourceRecordSetsWithContext(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to batch create dns records: %w", err)
	}

	return createdRecords, nil
}

// BatchUpdateDNSRecords 批量更新DNS记录
func (d *DNSClient) BatchUpdateDNSRecords(ctx context.Context, zoneID string, records []*cloud.DNSRecord) ([]*cloud.DNSRecord, error) {
	var changes []*route53.Change
	var updatedRecords []*cloud.DNSRecord

	for _, record := range records {
		var resourceRecords []*route53.ResourceRecord

		if record.Type == "MX" && record.Priority != nil {
			value := fmt.Sprintf("%d %s", *record.Priority, record.Value)
			resourceRecords = append(resourceRecords, &route53.ResourceRecord{
				Value: aws.String(value),
			})
		} else {
			resourceRecords = append(resourceRecords, &route53.ResourceRecord{
				Value: aws.String(record.Value),
			})
		}

		change := &route53.Change{
			Action: aws.String("UPSERT"),
			ResourceRecordSet: &route53.ResourceRecordSet{
				Name:            aws.String(record.Name),
				Type:            aws.String(record.Type),
				TTL:             aws.Int64(int64(record.TTL)),
				ResourceRecords: resourceRecords,
			},
		}
		changes = append(changes, change)

		updatedRecord := &cloud.DNSRecord{
			ID:          record.ID,
			ZoneID:      zoneID,
			Name:        record.Name,
			Type:        record.Type,
			Value:       record.Value,
			TTL:         record.TTL,
			Priority:    record.Priority,
			Status:      "active",
			Description: record.Description,
			Tags:        record.Tags,
			CreatedAt:   record.CreatedAt,
			UpdatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
		}
		updatedRecords = append(updatedRecords, updatedRecord)
	}

	input := &route53.ChangeResourceRecordSetsInput{
		HostedZoneId: aws.String(zoneID),
		ChangeBatch: &route53.ChangeBatch{
			Changes: changes,
		},
	}

	_, err := d.client.ChangeResourceRecordSetsWithContext(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to batch update dns records: %w", err)
	}

	return updatedRecords, nil
}

// BatchDeleteDNSRecords 批量删除DNS记录
func (d *DNSClient) BatchDeleteDNSRecords(ctx context.Context, zoneID string, recordIDs []string) error {
	var errors []error

	for _, recordID := range recordIDs {
		err := d.DeleteDNSRecord(ctx, zoneID, recordID)
		if err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("batch delete failed with %d errors: %v", len(errors), errors[0])
	}

	return nil
}

// ValidateDNSRecord 验证DNS记录
func (d *DNSClient) ValidateDNSRecord(ctx context.Context, record *cloud.DNSRecord) error {
	if record.Name == "" {
		return fmt.Errorf("record name cannot be empty")
	}
	if record.Type == "" {
		return fmt.Errorf("record type cannot be empty")
	}
	if record.Value == "" {
		return fmt.Errorf("record value cannot be empty")
	}

	switch record.Type {
	case "MX":
		if record.Priority == nil {
			return fmt.Errorf("MX record must have priority")
		}
	case "SRV":
		if record.Priority == nil || record.Weight == nil || record.Port == nil {
			return fmt.Errorf("SRV record must have priority, weight and port")
		}
	}

	return nil
}

// CheckDNSResolution 检查DNS解析
func (d *DNSClient) CheckDNSResolution(ctx context.Context, domain, recordType string) (*cloud.DNSResolutionResult, error) {
	result := &cloud.DNSResolutionResult{
		Domain:       domain,
		RecordType:   recordType,
		Values:       []string{},
		TTL:          0,
		ResponseTime: 0,
		Status:       "success",
	}

	return result, nil
}
