package aliyun

import (
	"context"
	"fmt"
	"time"

	"cmdb-platform/pkg/cloud"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/alidns"
)

// DNSClient 阿里云DNS客户端
type DNSClient struct {
	client *alidns.Client
}

// NewDNSClient 创建阿里云DNS客户端
func NewDNSClient(accessKeyID, accessKeySecret, region string) (*DNSClient, error) {
	client, err := alidns.NewClientWithAccessKey(region, accessKeyID, accessKeySecret)
	if err != nil {
		return nil, fmt.Errorf("failed to create aliyun dns client: %w", err)
	}

	return &DNSClient{
		client: client,
	}, nil
}

// ListDNSZones 列出DNS域名
func (d *DNSClient) ListDNSZones(ctx context.Context) ([]cloud.DNSZone, error) {
	request := alidns.CreateDescribeDomainsRequest()
	request.Scheme = "https"
	request.PageSize = "100"

	var allZones []cloud.DNSZone
	pageNumber := 1

	for {
		request.PageNumber = requests.NewInteger(pageNumber)
		response, err := d.client.DescribeDomains(request)
		if err != nil {
			return nil, fmt.Errorf("failed to list dns zones: %w", err)
		}

		for _, domain := range response.Domains.Domain {
			zone := cloud.DNSZone{
				ID:          domain.DomainId,
				DomainName:  domain.DomainName,
				Status:      "active", // 阿里云默认状态
				TTL:         600,      // 默认TTL
				Description: domain.Remark,
				Tags:        make(map[string]string),
				CreatedAt:   domain.CreateTime,
				UpdatedAt:   domain.CreateTime,
			}
			allZones = append(allZones, zone)
		}

		if len(response.Domains.Domain) < 100 {
			break
		}
		pageNumber++
	}

	return allZones, nil
}

// GetDNSZone 获取DNS域名详情
func (d *DNSClient) GetDNSZone(ctx context.Context, zoneID string) (*cloud.DNSZone, error) {
	request := alidns.CreateDescribeDomainInfoRequest()
	request.Scheme = "https"
	request.DomainName = zoneID // 阿里云使用域名作为标识

	response, err := d.client.DescribeDomainInfo(request)
	if err != nil {
		return nil, fmt.Errorf("failed to get dns zone: %w", err)
	}

	zone := &cloud.DNSZone{
		ID:          response.DomainId,
		DomainName:  response.DomainName,
		Status:      "active", // 阿里云默认状态
		TTL:         600,      // 默认TTL
		Description: response.Remark,
		Tags:        make(map[string]string),
		CreatedAt:   response.CreateTime,
		UpdatedAt:   response.CreateTime,
	}

	return zone, nil
}

// CreateDNSZone 创建DNS域名
func (d *DNSClient) CreateDNSZone(ctx context.Context, zone *cloud.DNSZone) (*cloud.DNSZone, error) {
	request := alidns.CreateAddDomainRequest()
	request.Scheme = "https"
	request.DomainName = zone.DomainName

	response, err := d.client.AddDomain(request)
	if err != nil {
		return nil, fmt.Errorf("failed to create dns zone: %w", err)
	}

	// 返回创建的域名信息
	createdZone := &cloud.DNSZone{
		ID:          response.DomainId,
		DomainName:  zone.DomainName,
		Status:      "active",
		TTL:         zone.TTL,
		Description: zone.Description,
		Tags:        zone.Tags,
		CreatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
		UpdatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
	}

	return createdZone, nil
}

// UpdateDNSZone 更新DNS域名
func (d *DNSClient) UpdateDNSZone(ctx context.Context, zoneID string, zone *cloud.DNSZone) (*cloud.DNSZone, error) {
	// 阿里云DNS域名更新功能有限，主要是更新备注
	request := alidns.CreateUpdateDomainRemarkRequest()
	request.Scheme = "https"
	request.DomainName = zoneID
	request.Remark = zone.Description

	_, err := d.client.UpdateDomainRemark(request)
	if err != nil {
		return nil, fmt.Errorf("failed to update dns zone: %w", err)
	}

	// 获取更新后的域名信息
	return d.GetDNSZone(ctx, zoneID)
}

// DeleteDNSZone 删除DNS域名
func (d *DNSClient) DeleteDNSZone(ctx context.Context, zoneID string) error {
	request := alidns.CreateDeleteDomainRequest()
	request.Scheme = "https"
	request.DomainName = zoneID

	_, err := d.client.DeleteDomain(request)
	if err != nil {
		return fmt.Errorf("failed to delete dns zone: %w", err)
	}

	return nil
}

// ListDNSRecords 列出DNS记录
func (d *DNSClient) ListDNSRecords(ctx context.Context, zoneID string) ([]cloud.DNSRecord, error) {
	request := alidns.CreateDescribeDomainRecordsRequest()
	request.Scheme = "https"
	request.DomainName = zoneID
	request.PageSize = "500"

	var allRecords []cloud.DNSRecord
	pageNumber := 1

	for {
		request.PageNumber = requests.NewInteger(pageNumber)
		response, err := d.client.DescribeDomainRecords(request)
		if err != nil {
			return nil, fmt.Errorf("failed to list dns records: %w", err)
		}

		for _, record := range response.DomainRecords.Record {
			dnsRecord := cloud.DNSRecord{
				ID:          record.RecordId,
				ZoneID:      zoneID,
				Name:        record.RR,
				Type:        record.Type,
				Value:       record.Value,
				TTL:         int(record.TTL),
				Status:      record.Status,
				Description: record.Remark,
				Tags:        make(map[string]string),
				CreatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
				UpdatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
			}

			// 处理MX记录的优先级
			if record.Type == "MX" && record.Priority != 0 {
				priority := int(record.Priority)
				dnsRecord.Priority = &priority
			}

			allRecords = append(allRecords, dnsRecord)
		}

		if len(response.DomainRecords.Record) < 500 {
			break
		}
		pageNumber++
	}

	return allRecords, nil
}

// GetDNSRecord 获取DNS记录详情
func (d *DNSClient) GetDNSRecord(ctx context.Context, zoneID, recordID string) (*cloud.DNSRecord, error) {
	request := alidns.CreateDescribeDomainRecordInfoRequest()
	request.Scheme = "https"
	request.RecordId = recordID

	response, err := d.client.DescribeDomainRecordInfo(request)
	if err != nil {
		return nil, fmt.Errorf("failed to get dns record: %w", err)
	}

	record := &cloud.DNSRecord{
		ID:          response.RecordId,
		ZoneID:      zoneID,
		Name:        response.RR,
		Type:        response.Type,
		Value:       response.Value,
		TTL:         int(response.TTL),
		Status:      response.Status,
		Description: response.Remark,
		Tags:        make(map[string]string),
		CreatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
		UpdatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
	}

	// 处理MX记录的优先级
	if response.Type == "MX" && response.Priority != 0 {
		priority := int(response.Priority)
		record.Priority = &priority
	}

	return record, nil
}

// CreateDNSRecord 创建DNS记录
func (d *DNSClient) CreateDNSRecord(ctx context.Context, zoneID string, record *cloud.DNSRecord) (*cloud.DNSRecord, error) {
	request := alidns.CreateAddDomainRecordRequest()
	request.Scheme = "https"
	request.DomainName = zoneID
	request.RR = record.Name
	request.Type = record.Type
	request.Value = record.Value
	request.TTL = requests.NewInteger(record.TTL)

	// 处理MX记录的优先级
	if record.Type == "MX" && record.Priority != nil {
		request.Priority = requests.NewInteger(*record.Priority)
	}

	response, err := d.client.AddDomainRecord(request)
	if err != nil {
		return nil, fmt.Errorf("failed to create dns record: %w", err)
	}

	// 返回创建的记录信息
	createdRecord := &cloud.DNSRecord{
		ID:          response.RecordId,
		ZoneID:      zoneID,
		Name:        record.Name,
		Type:        record.Type,
		Value:       record.Value,
		TTL:         record.TTL,
		Priority:    record.Priority,
		Status:      "enable",
		Description: record.Description,
		Tags:        record.Tags,
		CreatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
		UpdatedAt:   time.Now().Format("2006-01-02T15:04:05Z"),
	}

	return createdRecord, nil
}

// UpdateDNSRecord 更新DNS记录
func (d *DNSClient) UpdateDNSRecord(ctx context.Context, zoneID, recordID string, record *cloud.DNSRecord) (*cloud.DNSRecord, error) {
	request := alidns.CreateUpdateDomainRecordRequest()
	request.Scheme = "https"
	request.RecordId = recordID
	request.RR = record.Name
	request.Type = record.Type
	request.Value = record.Value
	request.TTL = requests.NewInteger(record.TTL)

	// 处理MX记录的优先级
	if record.Type == "MX" && record.Priority != nil {
		request.Priority = requests.NewInteger(*record.Priority)
	}

	_, err := d.client.UpdateDomainRecord(request)
	if err != nil {
		return nil, fmt.Errorf("failed to update dns record: %w", err)
	}

	// 返回更新后的记录信息
	return d.GetDNSRecord(ctx, zoneID, recordID)
}

// DeleteDNSRecord 删除DNS记录
func (d *DNSClient) DeleteDNSRecord(ctx context.Context, zoneID, recordID string) error {
	request := alidns.CreateDeleteDomainRecordRequest()
	request.Scheme = "https"
	request.RecordId = recordID

	_, err := d.client.DeleteDomainRecord(request)
	if err != nil {
		return fmt.Errorf("failed to delete dns record: %w", err)
	}

	return nil
}

// BatchCreateDNSRecords 批量创建DNS记录
func (d *DNSClient) BatchCreateDNSRecords(ctx context.Context, zoneID string, records []*cloud.DNSRecord) ([]*cloud.DNSRecord, error) {
	var createdRecords []*cloud.DNSRecord
	var errors []error

	for _, record := range records {
		createdRecord, err := d.CreateDNSRecord(ctx, zoneID, record)
		if err != nil {
			errors = append(errors, err)
			continue
		}
		createdRecords = append(createdRecords, createdRecord)
	}

	if len(errors) > 0 {
		return createdRecords, fmt.Errorf("batch create failed with %d errors: %v", len(errors), errors[0])
	}

	return createdRecords, nil
}

// BatchUpdateDNSRecords 批量更新DNS记录
func (d *DNSClient) BatchUpdateDNSRecords(ctx context.Context, zoneID string, records []*cloud.DNSRecord) ([]*cloud.DNSRecord, error) {
	var updatedRecords []*cloud.DNSRecord
	var errors []error

	for _, record := range records {
		updatedRecord, err := d.UpdateDNSRecord(ctx, zoneID, record.ID, record)
		if err != nil {
			errors = append(errors, err)
			continue
		}
		updatedRecords = append(updatedRecords, updatedRecord)
	}

	if len(errors) > 0 {
		return updatedRecords, fmt.Errorf("batch update failed with %d errors: %v", len(errors), errors[0])
	}

	return updatedRecords, nil
}

// BatchDeleteDNSRecords 批量删除DNS记录
func (d *DNSClient) BatchDeleteDNSRecords(ctx context.Context, zoneID string, recordIDs []string) error {
	var errors []error

	for _, recordID := range recordIDs {
		err := d.DeleteDNSRecord(ctx, zoneID, recordID)
		if err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("batch delete failed with %d errors: %v", len(errors), errors[0])
	}

	return nil
}

// ValidateDNSRecord 验证DNS记录
func (d *DNSClient) ValidateDNSRecord(ctx context.Context, record *cloud.DNSRecord) error {
	// 基本验证
	if record.Name == "" {
		return fmt.Errorf("record name cannot be empty")
	}
	if record.Type == "" {
		return fmt.Errorf("record type cannot be empty")
	}
	if record.Value == "" {
		return fmt.Errorf("record value cannot be empty")
	}

	// 类型特定验证
	switch record.Type {
	case "MX":
		if record.Priority == nil {
			return fmt.Errorf("MX record must have priority")
		}
	case "SRV":
		if record.Priority == nil || record.Weight == nil || record.Port == nil {
			return fmt.Errorf("SRV record must have priority, weight and port")
		}
	}

	return nil
}

// CheckDNSResolution 检查DNS解析
func (d *DNSClient) CheckDNSResolution(ctx context.Context, domain, recordType string) (*cloud.DNSResolutionResult, error) {
	// 阿里云DNS不直接提供解析检查API，这里返回基本信息
	// 实际项目中可以使用第三方DNS查询库
	result := &cloud.DNSResolutionResult{
		Domain:       domain,
		RecordType:   recordType,
		Values:       []string{},
		TTL:          0,
		ResponseTime: 0,
		Status:       "success",
	}

	return result, nil
}
