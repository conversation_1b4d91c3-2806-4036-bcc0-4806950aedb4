package tencent

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"cmdb-platform/pkg/cloud"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/dnspod"
)

// DNSClient 腾讯云DNS客户端
type DNSClient struct {
	client *dnspod.Client
}

// NewDNSClient 创建腾讯云DNS客户端
func NewDNSClient(secretID, secretKey, region string) (*DNSClient, error) {
	credential := common.NewCredential(secretID, secretKey)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "dnspod.tencentcloudapi.com"

	client, err := dnspod.NewClient(credential, region, cpf)
	if err != nil {
		return nil, fmt.Errorf("failed to create tencent dns client: %w", err)
	}

	return &DNSClient{
		client: client,
	}, nil
}

// ListDNSZones 列出DNS域名
func (d *DNSClient) ListDNSZones(ctx context.Context) ([]cloud.DNSZone, error) {
	request := dnspod.NewDescribeDomainListRequest()
	request.Limit = common.Uint64Ptr(100)

	var allZones []cloud.DNSZone
	offset := uint64(0)

	for {
		request.Offset = common.Uint64Ptr(offset)
		response, err := d.client.DescribeDomainList(request)
		if err != nil {
			if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
				return nil, fmt.Errorf("failed to list domains: %s", sdkError.Message)
			}
			return nil, fmt.Errorf("failed to list domains: %w", err)
		}

		for _, domain := range response.Response.DomainList {
			zone := cloud.DNSZone{
				ID:          strconv.FormatUint(*domain.DomainId, 10),
				DomainName:  *domain.Name,
				Status:      *domain.Status,
				TTL:         600, // 默认TTL
				Description: "",
				Tags:        make(map[string]string),
				CreatedAt:   *domain.CreatedOn,
				UpdatedAt:   *domain.UpdatedOn,
			}

			if domain.Remark != nil {
				zone.Description = *domain.Remark
			}

			allZones = append(allZones, zone)
		}

		if len(response.Response.DomainList) < 100 {
			break
		}
		offset += 100
	}

	return allZones, nil
}

// GetDNSZone 获取DNS域名详情
func (d *DNSClient) GetDNSZone(ctx context.Context, zoneID string) (*cloud.DNSZone, error) {
	domainID, err := strconv.ParseUint(zoneID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid zone ID: %s", zoneID)
	}

	request := dnspod.NewDescribeDomainRequest()
	request.DomainId = common.Uint64Ptr(domainID)

	response, err := d.client.DescribeDomain(request)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			return nil, fmt.Errorf("failed to get domain: %s", sdkError.Message)
		}
		return nil, fmt.Errorf("failed to get domain: %w", err)
	}

	domain := response.Response.DomainInfo
	zone := &cloud.DNSZone{
		ID:          strconv.FormatUint(*domain.DomainId, 10),
		DomainName:  *domain.Name,
		Status:      *domain.Status,
		TTL:         600,
		Description: "",
		Tags:        make(map[string]string),
		CreatedAt:   *domain.CreatedOn,
		UpdatedAt:   *domain.UpdatedOn,
	}

	if domain.Remark != nil {
		zone.Description = *domain.Remark
	}

	return zone, nil
}

// CreateDNSZone 创建DNS域名
func (d *DNSClient) CreateDNSZone(ctx context.Context, zone *cloud.DNSZone) (*cloud.DNSZone, error) {
	request := dnspod.NewCreateDomainRequest()
	request.Domain = common.StringPtr(zone.DomainName)

	if zone.Description != "" {
		request.Remark = common.StringPtr(zone.Description)
	}

	response, err := d.client.CreateDomain(request)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			return nil, fmt.Errorf("failed to create domain: %s", sdkError.Message)
		}
		return nil, fmt.Errorf("failed to create domain: %w", err)
	}

	createdZone := &cloud.DNSZone{
		ID:          strconv.FormatUint(*response.Response.DomainInfo.DomainId, 10),
		DomainName:  zone.DomainName,
		Status:      "enable",
		TTL:         zone.TTL,
		Description: zone.Description,
		Tags:        zone.Tags,
		CreatedAt:   time.Now().Format("2006-01-02 15:04:05"),
		UpdatedAt:   time.Now().Format("2006-01-02 15:04:05"),
	}

	return createdZone, nil
}

// UpdateDNSZone 更新DNS域名
func (d *DNSClient) UpdateDNSZone(ctx context.Context, zoneID string, zone *cloud.DNSZone) (*cloud.DNSZone, error) {
	domainID, err := strconv.ParseUint(zoneID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid zone ID: %s", zoneID)
	}

	request := dnspod.NewModifyDomainRemarkRequest()
	request.DomainId = common.Uint64Ptr(domainID)
	request.Remark = common.StringPtr(zone.Description)

	_, err = d.client.ModifyDomainRemark(request)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			return nil, fmt.Errorf("failed to update domain: %s", sdkError.Message)
		}
		return nil, fmt.Errorf("failed to update domain: %w", err)
	}

	return d.GetDNSZone(ctx, zoneID)
}

// DeleteDNSZone 删除DNS域名
func (d *DNSClient) DeleteDNSZone(ctx context.Context, zoneID string) error {
	domainID, err := strconv.ParseUint(zoneID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid zone ID: %s", zoneID)
	}

	request := dnspod.NewDeleteDomainRequest()
	request.DomainId = common.Uint64Ptr(domainID)

	_, err = d.client.DeleteDomain(request)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			return fmt.Errorf("failed to delete domain: %s", sdkError.Message)
		}
		return fmt.Errorf("failed to delete domain: %w", err)
	}

	return nil
}

// ListDNSRecords 列出DNS记录
func (d *DNSClient) ListDNSRecords(ctx context.Context, zoneID string) ([]cloud.DNSRecord, error) {
	domainID, err := strconv.ParseUint(zoneID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid zone ID: %s", zoneID)
	}

	request := dnspod.NewDescribeRecordListRequest()
	request.DomainId = common.Uint64Ptr(domainID)
	request.Limit = common.Uint64Ptr(3000)

	var allRecords []cloud.DNSRecord
	offset := uint64(0)

	for {
		request.Offset = common.Uint64Ptr(offset)
		response, err := d.client.DescribeRecordList(request)
		if err != nil {
			if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
				return nil, fmt.Errorf("failed to list records: %s", sdkError.Message)
			}
			return nil, fmt.Errorf("failed to list records: %w", err)
		}

		for _, record := range response.Response.RecordList {
			dnsRecord := cloud.DNSRecord{
				ID:          strconv.FormatUint(*record.RecordId, 10),
				ZoneID:      zoneID,
				Name:        *record.Name,
				Type:        *record.Type,
				Value:       *record.Value,
				TTL:         int(*record.TTL),
				Status:      *record.Status,
				Description: "",
				Tags:        make(map[string]string),
				CreatedAt:   *record.UpdatedOn,
				UpdatedAt:   *record.UpdatedOn,
			}

			// 处理MX记录的优先级
			if *record.Type == "MX" && record.MX != nil {
				priority := int(*record.MX)
				dnsRecord.Priority = &priority
			}

			if record.Remark != nil {
				dnsRecord.Description = *record.Remark
			}

			allRecords = append(allRecords, dnsRecord)
		}

		if len(response.Response.RecordList) < 3000 {
			break
		}
		offset += 3000
	}

	return allRecords, nil
}

// GetDNSRecord 获取DNS记录详情
func (d *DNSClient) GetDNSRecord(ctx context.Context, zoneID, recordID string) (*cloud.DNSRecord, error) {
	domainID, err := strconv.ParseUint(zoneID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid zone ID: %s", zoneID)
	}

	recordIDUint, err := strconv.ParseUint(recordID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid record ID: %s", recordID)
	}

	request := dnspod.NewDescribeRecordRequest()
	request.DomainId = common.Uint64Ptr(domainID)
	request.RecordId = common.Uint64Ptr(recordIDUint)

	response, err := d.client.DescribeRecord(request)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			return nil, fmt.Errorf("failed to get record: %s", sdkError.Message)
		}
		return nil, fmt.Errorf("failed to get record: %w", err)
	}

	record := response.Response.RecordInfo
	dnsRecord := &cloud.DNSRecord{
		ID:          strconv.FormatUint(*record.RecordId, 10),
		ZoneID:      zoneID,
		Name:        *record.Name,
		Type:        *record.Type,
		Value:       *record.Value,
		TTL:         int(*record.TTL),
		Status:      *record.Status,
		Description: "",
		Tags:        make(map[string]string),
		CreatedAt:   *record.UpdatedOn,
		UpdatedAt:   *record.UpdatedOn,
	}

	// 处理MX记录的优先级
	if *record.Type == "MX" && record.MX != nil {
		priority := int(*record.MX)
		dnsRecord.Priority = &priority
	}

	if record.Remark != nil {
		dnsRecord.Description = *record.Remark
	}

	return dnsRecord, nil
}

// CreateDNSRecord 创建DNS记录
func (d *DNSClient) CreateDNSRecord(ctx context.Context, zoneID string, record *cloud.DNSRecord) (*cloud.DNSRecord, error) {
	domainID, err := strconv.ParseUint(zoneID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid zone ID: %s", zoneID)
	}

	request := dnspod.NewCreateRecordRequest()
	request.DomainId = common.Uint64Ptr(domainID)
	request.SubDomain = common.StringPtr(record.Name)
	request.RecordType = common.StringPtr(record.Type)
	request.Value = common.StringPtr(record.Value)
	request.TTL = common.Uint64Ptr(uint64(record.TTL))

	// 处理MX记录的优先级
	if record.Type == "MX" && record.Priority != nil {
		request.MX = common.Uint64Ptr(uint64(*record.Priority))
	}

	if record.Description != "" {
		request.Remark = common.StringPtr(record.Description)
	}

	response, err := d.client.CreateRecord(request)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			return nil, fmt.Errorf("failed to create record: %s", sdkError.Message)
		}
		return nil, fmt.Errorf("failed to create record: %w", err)
	}

	createdRecord := &cloud.DNSRecord{
		ID:          strconv.FormatUint(*response.Response.RecordId, 10),
		ZoneID:      zoneID,
		Name:        record.Name,
		Type:        record.Type,
		Value:       record.Value,
		TTL:         record.TTL,
		Priority:    record.Priority,
		Status:      "enable",
		Description: record.Description,
		Tags:        record.Tags,
		CreatedAt:   time.Now().Format("2006-01-02 15:04:05"),
		UpdatedAt:   time.Now().Format("2006-01-02 15:04:05"),
	}

	return createdRecord, nil
}

// UpdateDNSRecord 更新DNS记录
func (d *DNSClient) UpdateDNSRecord(ctx context.Context, zoneID, recordID string, record *cloud.DNSRecord) (*cloud.DNSRecord, error) {
	domainID, err := strconv.ParseUint(zoneID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid zone ID: %s", zoneID)
	}

	recordIDUint, err := strconv.ParseUint(recordID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid record ID: %s", recordID)
	}

	request := dnspod.NewModifyRecordRequest()
	request.DomainId = common.Uint64Ptr(domainID)
	request.RecordId = common.Uint64Ptr(recordIDUint)
	request.SubDomain = common.StringPtr(record.Name)
	request.RecordType = common.StringPtr(record.Type)
	request.Value = common.StringPtr(record.Value)
	request.TTL = common.Uint64Ptr(uint64(record.TTL))

	// 处理MX记录的优先级
	if record.Type == "MX" && record.Priority != nil {
		request.MX = common.Uint64Ptr(uint64(*record.Priority))
	}

	if record.Description != "" {
		request.Remark = common.StringPtr(record.Description)
	}

	_, err = d.client.ModifyRecord(request)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			return nil, fmt.Errorf("failed to update record: %s", sdkError.Message)
		}
		return nil, fmt.Errorf("failed to update record: %w", err)
	}

	return d.GetDNSRecord(ctx, zoneID, recordID)
}

// DeleteDNSRecord 删除DNS记录
func (d *DNSClient) DeleteDNSRecord(ctx context.Context, zoneID, recordID string) error {
	domainID, err := strconv.ParseUint(zoneID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid zone ID: %s", zoneID)
	}

	recordIDUint, err := strconv.ParseUint(recordID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid record ID: %s", recordID)
	}

	request := dnspod.NewDeleteRecordRequest()
	request.DomainId = common.Uint64Ptr(domainID)
	request.RecordId = common.Uint64Ptr(recordIDUint)

	_, err = d.client.DeleteRecord(request)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			return fmt.Errorf("failed to delete record: %s", sdkError.Message)
		}
		return fmt.Errorf("failed to delete record: %w", err)
	}

	return nil
}

// BatchCreateDNSRecords 批量创建DNS记录
func (d *DNSClient) BatchCreateDNSRecords(ctx context.Context, zoneID string, records []*cloud.DNSRecord) ([]*cloud.DNSRecord, error) {
	var createdRecords []*cloud.DNSRecord
	var errors []error

	for _, record := range records {
		createdRecord, err := d.CreateDNSRecord(ctx, zoneID, record)
		if err != nil {
			errors = append(errors, err)
			continue
		}
		createdRecords = append(createdRecords, createdRecord)
	}

	if len(errors) > 0 {
		return createdRecords, fmt.Errorf("batch create failed with %d errors: %v", len(errors), errors[0])
	}

	return createdRecords, nil
}

// BatchUpdateDNSRecords 批量更新DNS记录
func (d *DNSClient) BatchUpdateDNSRecords(ctx context.Context, zoneID string, records []*cloud.DNSRecord) ([]*cloud.DNSRecord, error) {
	var updatedRecords []*cloud.DNSRecord
	var errors []error

	for _, record := range records {
		updatedRecord, err := d.UpdateDNSRecord(ctx, zoneID, record.ID, record)
		if err != nil {
			errors = append(errors, err)
			continue
		}
		updatedRecords = append(updatedRecords, updatedRecord)
	}

	if len(errors) > 0 {
		return updatedRecords, fmt.Errorf("batch update failed with %d errors: %v", len(errors), errors[0])
	}

	return updatedRecords, nil
}

// BatchDeleteDNSRecords 批量删除DNS记录
func (d *DNSClient) BatchDeleteDNSRecords(ctx context.Context, zoneID string, recordIDs []string) error {
	var errors []error

	for _, recordID := range recordIDs {
		err := d.DeleteDNSRecord(ctx, zoneID, recordID)
		if err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("batch delete failed with %d errors: %v", len(errors), errors[0])
	}

	return nil
}

// ValidateDNSRecord 验证DNS记录
func (d *DNSClient) ValidateDNSRecord(ctx context.Context, record *cloud.DNSRecord) error {
	if record.Name == "" {
		return fmt.Errorf("record name cannot be empty")
	}
	if record.Type == "" {
		return fmt.Errorf("record type cannot be empty")
	}
	if record.Value == "" {
		return fmt.Errorf("record value cannot be empty")
	}

	switch record.Type {
	case "MX":
		if record.Priority == nil {
			return fmt.Errorf("MX record must have priority")
		}
	case "SRV":
		if record.Priority == nil || record.Weight == nil || record.Port == nil {
			return fmt.Errorf("SRV record must have priority, weight and port")
		}
	}

	return nil
}

// CheckDNSResolution 检查DNS解析
func (d *DNSClient) CheckDNSResolution(ctx context.Context, domain, recordType string) (*cloud.DNSResolutionResult, error) {
	result := &cloud.DNSResolutionResult{
		Domain:       domain,
		RecordType:   recordType,
		Values:       []string{},
		TTL:          0,
		ResponseTime: 0,
		Status:       "success",
	}

	return result, nil
}
