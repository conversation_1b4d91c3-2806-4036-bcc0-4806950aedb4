package tencent

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"cmdb-platform/pkg/cloud"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/dnspod"
)

// DNSClient 腾讯云DNS客户端
type DNSClient struct {
	client *dnspod.Client
}

// NewDNSClient 创建腾讯云DNS客户端
func NewDNSClient(secretID, secretKey, region string) (*DNSClient, error) {
	credential := common.NewCredential(secretID, secretKey)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "dnspod.tencentcloudapi.com"

	client, err := dnspod.NewClient(credential, region, cpf)
	if err != nil {
		return nil, fmt.Errorf("failed to create tencent dns client: %w", err)
	}

	return &DNSClient{
		client: client,
	}, nil
}

// ListDNSZones 列出DNS域名
func (d *DNSClient) ListDNSZones(ctx context.Context) ([]cloud.DNSZone, error) {
	request := dnspod.NewDescribeDomainListRequest()
	request.Limit = common.Uint64Ptr(100)

	var allZones []cloud.DNSZone
	offset := uint64(0)

	for {
		request.Offset = common.Uint64Ptr(offset)
		response, err := d.client.DescribeDomainList(request)
		if err != nil {
			if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
				return nil, fmt.Errorf("failed to list domains: %s", sdkError.Message)
			}
			return nil, fmt.Errorf("failed to list domains: %w", err)
		}

		for _, domain := range response.Response.DomainList {
			zone := cloud.DNSZone{
				ID:          strconv.FormatUint(*domain.DomainId, 10),
				DomainName:  *domain.Name,
				Status:      *domain.Status,
				TTL:         600, // 默认TTL
				Description: "",
				Tags:        make(map[string]string),
				CreatedAt:   *domain.CreatedOn,
				UpdatedAt:   *domain.UpdatedOn,
			}

			if domain.Remark != nil {
				zone.Description = *domain.Remark
			}

			allZones = append(allZones, zone)
		}

		if len(response.Response.DomainList) < 100 {
			break
		}
		offset += 100
	}

	return allZones, nil
}

// GetDNSZone 获取DNS域名详情
func (d *DNSClient) GetDNSZone(ctx context.Context, zoneID string) (*cloud.DNSZone, error) {
	domainID, err := strconv.ParseUint(zoneID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid zone ID: %s", zoneID)
	}

	request := dnspod.NewDescribeDomainRequest()
	request.DomainId = common.Uint64Ptr(domainID)

	response, err := d.client.DescribeDomain(request)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			return nil, fmt.Errorf("failed to get domain: %s", sdkError.Message)
		}
		return nil, fmt.Errorf("failed to get domain: %w", err)
	}

	domain := response.Response.DomainInfo
	zone := &cloud.DNSZone{
		ID:          strconv.FormatUint(*domain.DomainId, 10),
		DomainName:  *domain.Name,
		Status:      *domain.Status,
		TTL:         600,
		Description: "",
		Tags:        make(map[string]string),
		CreatedAt:   *domain.CreatedOn,
		UpdatedAt:   *domain.UpdatedOn,
	}

	if domain.Remark != nil {
		zone.Description = *domain.Remark
	}

	return zone, nil
}

// CreateDNSZone 创建DNS域名
func (d *DNSClient) CreateDNSZone(ctx context.Context, zone *cloud.DNSZone) (*cloud.DNSZone, error) {
	request := dnspod.NewCreateDomainRequest()
	request.Domain = common.StringPtr(zone.DomainName)

	if zone.Description != "" {
		request.Remark = common.StringPtr(zone.Description)
	}

	response, err := d.client.CreateDomain(request)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			return nil, fmt.Errorf("failed to create domain: %s", sdkError.Message)
		}
		return nil, fmt.Errorf("failed to create domain: %w", err)
	}

	createdZone := &cloud.DNSZone{
		ID:          strconv.FormatUint(*response.Response.DomainInfo.DomainId, 10),
		DomainName:  zone.DomainName,
		Status:      "enable",
		TTL:         zone.TTL,
		Description: zone.Description,
		Tags:        zone.Tags,
		CreatedAt:   time.Now().Format("2006-01-02 15:04:05"),
		UpdatedAt:   time.Now().Format("2006-01-02 15:04:05"),
	}

	return createdZone, nil
}

// UpdateDNSZone 更新DNS域名
func (d *DNSClient) UpdateDNSZone(ctx context.Context, zoneID string, zone *cloud.DNSZone) (*cloud.DNSZone, error) {
	domainID, err := strconv.ParseUint(zoneID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid zone ID: %s", zoneID)
	}

	request := dnspod.NewModifyDomainRemarkRequest()
	request.DomainId = common.Uint64Ptr(domainID)
	request.Remark = common.StringPtr(zone.Description)

	_, err = d.client.ModifyDomainRemark(request)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			return nil, fmt.Errorf("failed to update domain: %s", sdkError.Message)
		}
		return nil, fmt.Errorf("failed to update domain: %w", err)
	}

	return d.GetDNSZone(ctx, zoneID)
}

// DeleteDNSZone 删除DNS域名
func (d *DNSClient) DeleteDNSZone(ctx context.Context, zoneID string) error {
	domainID, err := strconv.ParseUint(zoneID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid zone ID: %s", zoneID)
	}

	request := dnspod.NewDeleteDomainRequest()
	request.DomainId = common.Uint64Ptr(domainID)

	_, err = d.client.DeleteDomain(request)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			return fmt.Errorf("failed to delete domain: %s", sdkError.Message)
		}
		return fmt.Errorf("failed to delete domain: %w", err)
	}

	return nil
}

// ListDNSRecords 列出DNS记录
func (d *DNSClient) ListDNSRecords(ctx context.Context, zoneID string) ([]cloud.DNSRecord, error) {
	domainID, err := strconv.ParseUint(zoneID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid zone ID: %s", zoneID)
	}

	request := dnspod.NewDescribeRecordListRequest()
	request.DomainId = common.Uint64Ptr(domainID)
	request.Limit = common.Uint64Ptr(3000)

	var allRecords []cloud.DNSRecord
	offset := uint64(0)

	for {
		request.Offset = common.Uint64Ptr(offset)
		response, err := d.client.DescribeRecordList(request)
		if err != nil {
			if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
				return nil, fmt.Errorf("failed to list records: %s", sdkError.Message)
			}
			return nil, fmt.Errorf("failed to list records: %w", err)
		}

		for _, record := range response.Response.RecordList {
			dnsRecord := cloud.DNSRecord{
				ID:          strconv.FormatUint(*record.RecordId, 10),
				ZoneID:      zoneID,
				Name:        *record.Name,
				Type:        *record.Type,
				Value:       *record.Value,
				TTL:         int(*record.TTL),
				Status:      *record.Status,
				Description: "",
				Tags:        make(map[string]string),
				CreatedAt:   *record.UpdatedOn,
				UpdatedAt:   *record.UpdatedOn,
			}

			// 处理MX记录的优先级
			if *record.Type == "MX" && record.MX != nil {
				priority := int(*record.MX)
				dnsRecord.Priority = &priority
			}

			if record.Remark != nil {
				dnsRecord.Description = *record.Remark
			}

			allRecords = append(allRecords, dnsRecord)
		}

		if len(response.Response.RecordList) < 3000 {
			break
		}
		offset += 3000
	}

	return allRecords, nil
}
