package cloud

import (
	"context"

	"cmdb-platform/internal/model"
)

// CloudProvider 云服务商接口
type CloudProvider interface {
	// 基础信息
	GetProviderName() string
	GetRegions(ctx context.Context) ([]Region, error)
	GetZones(ctx context.Context, region string) ([]Zone, error)

	// 计算资源
	ListInstances(ctx context.Context, region string) ([]Instance, error)
	GetInstance(ctx context.Context, region, instanceID string) (*Instance, error)

	// 网络资源
	ListVPCs(ctx context.Context, region string) ([]VPC, error)
	GetVPC(ctx context.Context, region, vpcID string) (*VPC, error)
	ListSubnets(ctx context.Context, region, vpcID string) ([]Subnet, error)
	GetSubnet(ctx context.Context, region, subnetID string) (*Subnet, error)
	ListLoadBalancers(ctx context.Context, region string) ([]LoadBalancer, error)
	GetLoadBalancer(ctx context.Context, region, lbID string) (*LoadBalancer, error)

	// 存储资源
	ListDisks(ctx context.Context, region string) ([]Disk, error)
	GetDisk(ctx context.Context, region, diskID string) (*Disk, error)

	// 数据库资源
	ListDatabases(ctx context.Context, region string) ([]Database, error)
	GetDatabase(ctx context.Context, region, dbID string) (*Database, error)

	// 安全组
	ListSecurityGroups(ctx context.Context, region string) ([]SecurityGroup, error)
	GetSecurityGroup(ctx context.Context, region, sgID string) (*SecurityGroup, error)

	// DNS管理
	DNSProvider() DNSProvider
}

// DNSProvider DNS服务商接口
type DNSProvider interface {
	// DNS域名管理
	ListDNSZones(ctx context.Context) ([]DNSZone, error)
	GetDNSZone(ctx context.Context, zoneID string) (*DNSZone, error)
	CreateDNSZone(ctx context.Context, zone *DNSZone) (*DNSZone, error)
	UpdateDNSZone(ctx context.Context, zoneID string, zone *DNSZone) (*DNSZone, error)
	DeleteDNSZone(ctx context.Context, zoneID string) error

	// DNS记录管理
	ListDNSRecords(ctx context.Context, zoneID string) ([]DNSRecord, error)
	GetDNSRecord(ctx context.Context, zoneID, recordID string) (*DNSRecord, error)
	CreateDNSRecord(ctx context.Context, zoneID string, record *DNSRecord) (*DNSRecord, error)
	UpdateDNSRecord(ctx context.Context, zoneID, recordID string, record *DNSRecord) (*DNSRecord, error)
	DeleteDNSRecord(ctx context.Context, zoneID, recordID string) error

	// 批量操作
	BatchCreateDNSRecords(ctx context.Context, zoneID string, records []*DNSRecord) ([]*DNSRecord, error)
	BatchUpdateDNSRecords(ctx context.Context, zoneID string, records []*DNSRecord) ([]*DNSRecord, error)
	BatchDeleteDNSRecords(ctx context.Context, zoneID string, recordIDs []string) error

	// DNS解析验证
	ValidateDNSRecord(ctx context.Context, record *DNSRecord) error
	CheckDNSResolution(ctx context.Context, domain, recordType string) (*DNSResolutionResult, error)
}

// CloudResourceConverter 云资源转换器接口
type CloudResourceConverter interface {
	ConvertToResource(cloudResource interface{}) (*model.Resource, error)
	ConvertToConfigItems(cloudResource interface{}) ([]model.ConfigItem, error)
}

// Region 地域信息
type Region struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Status      string `json:"status"`
}

// Zone 可用区信息
type Zone struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Status      string `json:"status"`
	RegionID    string `json:"region_id"`
}

// Instance 云服务器实例
type Instance struct {
	ID               string            `json:"id"`
	Name             string            `json:"name"`
	Status           string            `json:"status"`
	InstanceType     string            `json:"instance_type"`
	CPU              int               `json:"cpu"`
	Memory           int               `json:"memory"`
	OSType           string            `json:"os_type"`
	OSName           string            `json:"os_name"`
	PublicIP         string            `json:"public_ip"`
	PrivateIP        string            `json:"private_ip"`
	VpcID            string            `json:"vpc_id"`
	SubnetID         string            `json:"subnet_id"`
	SecurityGroupIDs []string          `json:"security_group_ids"`
	Tags             map[string]string `json:"tags"`
	CreatedAt        string            `json:"created_at"`
	Region           string            `json:"region"`
	Zone             string            `json:"zone"`
}

// VPC 虚拟私有云
type VPC struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Status      string            `json:"status"`
	CidrBlock   string            `json:"cidr_block"`
	Description string            `json:"description"`
	Tags        map[string]string `json:"tags"`
	CreatedAt   string            `json:"created_at"`
	Region      string            `json:"region"`
}

// Subnet 子网
type Subnet struct {
	ID               string            `json:"id"`
	Name             string            `json:"name"`
	Status           string            `json:"status"`
	VpcID            string            `json:"vpc_id"`
	CidrBlock        string            `json:"cidr_block"`
	AvailableIPCount int               `json:"available_ip_count"`
	Description      string            `json:"description"`
	Tags             map[string]string `json:"tags"`
	CreatedAt        string            `json:"created_at"`
	Region           string            `json:"region"`
	Zone             string            `json:"zone"`
}

// LoadBalancer 负载均衡器
type LoadBalancer struct {
	ID        string            `json:"id"`
	Name      string            `json:"name"`
	Status    string            `json:"status"`
	Type      string            `json:"type"`
	VpcID     string            `json:"vpc_id"`
	SubnetIDs []string          `json:"subnet_ids"`
	PublicIP  string            `json:"public_ip"`
	PrivateIP string            `json:"private_ip"`
	Listeners []LBListener      `json:"listeners"`
	Tags      map[string]string `json:"tags"`
	CreatedAt string            `json:"created_at"`
	Region    string            `json:"region"`
}

// LBListener 负载均衡监听器
type LBListener struct {
	Port     int    `json:"port"`
	Protocol string `json:"protocol"`
	Status   string `json:"status"`
}

// Disk 云盘
type Disk struct {
	ID         string            `json:"id"`
	Name       string            `json:"name"`
	Status     string            `json:"status"`
	Type       string            `json:"type"`
	Size       int               `json:"size"`
	InstanceID string            `json:"instance_id"`
	Device     string            `json:"device"`
	Encrypted  bool              `json:"encrypted"`
	Tags       map[string]string `json:"tags"`
	CreatedAt  string            `json:"created_at"`
	Region     string            `json:"region"`
	Zone       string            `json:"zone"`
}

// Database 数据库实例
type Database struct {
	ID           string            `json:"id"`
	Name         string            `json:"name"`
	Status       string            `json:"status"`
	Engine       string            `json:"engine"`
	Version      string            `json:"version"`
	InstanceType string            `json:"instance_type"`
	CPU          int               `json:"cpu"`
	Memory       int               `json:"memory"`
	Storage      int               `json:"storage"`
	VpcID        string            `json:"vpc_id"`
	SubnetID     string            `json:"subnet_id"`
	PublicIP     string            `json:"public_ip"`
	PrivateIP    string            `json:"private_ip"`
	Port         int               `json:"port"`
	Tags         map[string]string `json:"tags"`
	CreatedAt    string            `json:"created_at"`
	Region       string            `json:"region"`
	Zone         string            `json:"zone"`
}

// SecurityGroup 安全组
type SecurityGroup struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	VpcID       string            `json:"vpc_id"`
	Rules       []SecurityRule    `json:"rules"`
	Tags        map[string]string `json:"tags"`
	CreatedAt   string            `json:"created_at"`
	Region      string            `json:"region"`
}

// SecurityRule 安全组规则
type SecurityRule struct {
	Direction   string `json:"direction"`   // ingress, egress
	Protocol    string `json:"protocol"`    // tcp, udp, icmp, all
	Port        string `json:"port"`        // 端口范围
	Source      string `json:"source"`      // 源地址
	Destination string `json:"destination"` // 目标地址
	Action      string `json:"action"`      // allow, deny
	Priority    int    `json:"priority"`
	Description string `json:"description"`
}

// DNS相关结构体

// DNSZone DNS域名
type DNSZone struct {
	ID          string            `json:"id"`
	DomainName  string            `json:"domain_name"`
	Status      string            `json:"status"`
	TTL         int               `json:"ttl"`
	Description string            `json:"description"`
	Tags        map[string]string `json:"tags"`
	CreatedAt   string            `json:"created_at"`
	UpdatedAt   string            `json:"updated_at"`
}

// DNSRecord DNS记录
type DNSRecord struct {
	ID          string            `json:"id"`
	ZoneID      string            `json:"zone_id"`
	Name        string            `json:"name"`
	Type        string            `json:"type"`
	Value       string            `json:"value"`
	TTL         int               `json:"ttl"`
	Priority    *int              `json:"priority,omitempty"`
	Weight      *int              `json:"weight,omitempty"`
	Port        *int              `json:"port,omitempty"`
	Status      string            `json:"status"`
	Description string            `json:"description"`
	Tags        map[string]string `json:"tags"`
	CreatedAt   string            `json:"created_at"`
	UpdatedAt   string            `json:"updated_at"`
}

// DNSResolutionResult DNS解析结果
type DNSResolutionResult struct {
	Domain       string   `json:"domain"`
	RecordType   string   `json:"record_type"`
	Values       []string `json:"values"`
	TTL          int      `json:"ttl"`
	ResponseTime int      `json:"response_time"` // 毫秒
	Status       string   `json:"status"`        // success, failed
	Error        string   `json:"error,omitempty"`
}
