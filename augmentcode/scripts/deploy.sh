#!/bin/bash

# CMDB 平台部署脚本
# 使用方法: ./scripts/deploy.sh [环境] [版本]
# 示例: ./scripts/deploy.sh production v1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 1 ]; then
    log_error "使用方法: $0 <环境> [版本]"
    log_info "环境选项: development, staging, production"
    log_info "版本示例: v1.0.0 (可选，默认为 latest)"
    exit 1
fi

ENVIRONMENT=$1
VERSION=${2:-latest}
PROJECT_NAME="cmdb-platform"
DOCKER_REGISTRY="your-registry.com"
NAMESPACE="cmdb-platform"

log_info "开始部署 $PROJECT_NAME"
log_info "环境: $ENVIRONMENT"
log_info "版本: $VERSION"

# 检查必要的工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 构建 Docker 镜像
build_image() {
    log_info "构建 Docker 镜像..."
    
    IMAGE_TAG="$DOCKER_REGISTRY/$PROJECT_NAME:$VERSION"
    
    docker build -t $IMAGE_TAG .
    
    if [ $? -eq 0 ]; then
        log_success "镜像构建成功: $IMAGE_TAG"
    else
        log_error "镜像构建失败"
        exit 1
    fi
}

# 推送镜像到仓库
push_image() {
    log_info "推送镜像到仓库..."
    
    IMAGE_TAG="$DOCKER_REGISTRY/$PROJECT_NAME:$VERSION"
    
    docker push $IMAGE_TAG
    
    if [ $? -eq 0 ]; then
        log_success "镜像推送成功"
    else
        log_error "镜像推送失败"
        exit 1
    fi
}

# 创建 Kubernetes 命名空间
create_namespace() {
    log_info "创建命名空间..."
    
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    log_success "命名空间创建/更新完成"
}

# 部署配置文件
deploy_configs() {
    log_info "部署配置文件..."
    
    # 创建 ConfigMap
    kubectl create configmap $PROJECT_NAME-config \
        --from-file=configs/config.$ENVIRONMENT.yaml \
        --namespace=$NAMESPACE \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # 创建 Secret (如果存在)
    if [ -f "configs/secrets.$ENVIRONMENT.yaml" ]; then
        kubectl apply -f configs/secrets.$ENVIRONMENT.yaml -n $NAMESPACE
    fi
    
    log_success "配置文件部署完成"
}

# 部署应用
deploy_app() {
    log_info "部署应用..."
    
    # 替换部署文件中的变量
    sed -e "s/{{VERSION}}/$VERSION/g" \
        -e "s/{{ENVIRONMENT}}/$ENVIRONMENT/g" \
        -e "s/{{REGISTRY}}/$DOCKER_REGISTRY/g" \
        k8s/deployment.$ENVIRONMENT.yaml | kubectl apply -f - -n $NAMESPACE
    
    # 部署服务
    kubectl apply -f k8s/service.yaml -n $NAMESPACE
    
    # 部署 Ingress (如果存在)
    if [ -f "k8s/ingress.$ENVIRONMENT.yaml" ]; then
        kubectl apply -f k8s/ingress.$ENVIRONMENT.yaml -n $NAMESPACE
    fi
    
    log_success "应用部署完成"
}

# 等待部署完成
wait_for_deployment() {
    log_info "等待部署完成..."
    
    kubectl rollout status deployment/$PROJECT_NAME -n $NAMESPACE --timeout=300s
    
    if [ $? -eq 0 ]; then
        log_success "部署成功完成"
    else
        log_error "部署超时或失败"
        exit 1
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 获取服务端点
    SERVICE_IP=$(kubectl get svc $PROJECT_NAME -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    
    if [ -z "$SERVICE_IP" ]; then
        SERVICE_IP=$(kubectl get svc $PROJECT_NAME -n $NAMESPACE -o jsonpath='{.spec.clusterIP}')
    fi
    
    # 健康检查
    for i in {1..30}; do
        if curl -f http://$SERVICE_IP:8080/health &> /dev/null; then
            log_success "健康检查通过"
            return 0
        fi
        log_info "等待服务启动... ($i/30)"
        sleep 10
    done
    
    log_error "健康检查失败"
    return 1
}

# 显示部署信息
show_deployment_info() {
    log_info "部署信息:"
    echo "----------------------------------------"
    kubectl get pods -n $NAMESPACE -l app=$PROJECT_NAME
    echo "----------------------------------------"
    kubectl get svc -n $NAMESPACE -l app=$PROJECT_NAME
    echo "----------------------------------------"
    
    # 获取访问地址
    EXTERNAL_IP=$(kubectl get svc $PROJECT_NAME -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if [ -n "$EXTERNAL_IP" ]; then
        log_success "应用访问地址: http://$EXTERNAL_IP:8080"
    else
        log_info "使用端口转发访问应用:"
        log_info "kubectl port-forward -n $NAMESPACE svc/$PROJECT_NAME 8080:8080"
    fi
}

# 回滚函数
rollback() {
    log_warning "执行回滚..."
    kubectl rollout undo deployment/$PROJECT_NAME -n $NAMESPACE
    kubectl rollout status deployment/$PROJECT_NAME -n $NAMESPACE
    log_success "回滚完成"
}

# 清理函数
cleanup() {
    log_info "清理资源..."
    kubectl delete namespace $NAMESPACE
    log_success "清理完成"
}

# 主函数
main() {
    case $ENVIRONMENT in
        development|staging|production)
            log_info "部署到 $ENVIRONMENT 环境"
            ;;
        *)
            log_error "不支持的环境: $ENVIRONMENT"
            log_info "支持的环境: development, staging, production"
            exit 1
            ;;
    esac
    
    # 执行部署步骤
    check_dependencies
    
    # 如果是本地开发环境，跳过镜像构建和推送
    if [ "$ENVIRONMENT" != "development" ]; then
        build_image
        push_image
    fi
    
    create_namespace
    deploy_configs
    deploy_app
    wait_for_deployment
    
    if health_check; then
        show_deployment_info
        log_success "🎉 部署成功完成!"
    else
        log_error "❌ 部署失败，请检查日志"
        exit 1
    fi
}

# 处理命令行参数
case "${1:-}" in
    rollback)
        rollback
        ;;
    cleanup)
        cleanup
        ;;
    development|staging|production)
        main
        ;;
    *)
        log_error "使用方法: $0 <环境|命令> [版本]"
        log_info "环境: development, staging, production"
        log_info "命令: rollback, cleanup"
        exit 1
        ;;
esac
