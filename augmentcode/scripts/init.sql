-- CMDB 平台数据库初始化脚本

-- 创建数据库（如果不存在）
-- CREATE DATABASE cmdb_platform;

-- 使用数据库
-- \c cmdb_platform;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建索引函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建默认用户（密码: password）
-- 注意：这个脚本仅用于开发环境，生产环境请修改默认密码
INSERT INTO users (id, username, email, password, display_name, role, status, created_at, updated_at)
VALUES (
    uuid_generate_v4(),
    'admin',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password
    'Administrator',
    'admin',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (username) DO NOTHING;

-- 创建默认租户
INSERT INTO tenants (id, name, code, description, status, created_at, updated_at)
VALUES (
    uuid_generate_v4(),
    'Default Tenant',
    'default',
    'Default tenant for system',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (code) DO NOTHING;

-- 创建用户租户关联
INSERT INTO user_tenants (id, user_id, tenant_id, role, created_at, updated_at)
SELECT 
    uuid_generate_v4(),
    u.id,
    t.id,
    'admin',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM users u, tenants t
WHERE u.username = 'admin' AND t.code = 'default'
ON CONFLICT (user_id, tenant_id) DO NOTHING;

-- 创建示例云账号（仅用于演示）
INSERT INTO cloud_accounts (id, tenant_id, name, provider, access_key_id, access_key_secret, region, status, created_at, updated_at)
SELECT 
    uuid_generate_v4(),
    t.id,
    'Demo Aliyun Account',
    'aliyun',
    'demo-access-key',
    'demo-secret-key',
    'cn-hangzhou',
    'inactive',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM tenants t
WHERE t.code = 'default'
ON CONFLICT (name, tenant_id) DO NOTHING;

-- 创建示例资源
INSERT INTO resources (id, tenant_id, cloud_account_id, type, name, cloud_id, provider, region, zone, status, properties, tags, created_at, updated_at, last_sync_at)
SELECT 
    uuid_generate_v4(),
    t.id,
    ca.id,
    'ecs',
    'demo-web-server',
    'i-demo123456',
    'aliyun',
    'cn-hangzhou',
    'cn-hangzhou-a',
    'running',
    '{"instance_type": "ecs.t5-lc1m1.small", "cpu": 1, "memory": 1024}',
    '{"environment": "demo", "project": "cmdb"}',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM tenants t, cloud_accounts ca
WHERE t.code = 'default' AND ca.name = 'Demo Aliyun Account'
ON CONFLICT (cloud_id, cloud_account_id) DO NOTHING;

-- 创建示例告警
INSERT INTO alerts (id, tenant_id, resource_id, title, description, level, status, source, rule_id, tags, created_at, updated_at)
SELECT 
    uuid_generate_v4(),
    r.tenant_id,
    r.id,
    'Demo Alert: High CPU Usage',
    'CPU usage is above 80% for demo server',
    'warning',
    'open',
    'demo',
    'cpu_high',
    '{"metric": "cpu_usage", "threshold": 80}',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM resources r
WHERE r.name = 'demo-web-server'
ON CONFLICT DO NOTHING;

-- 创建示例变更记录
INSERT INTO change_records (id, tenant_id, resource_id, type, field, old_value, new_value, description, source, created_at, updated_at)
SELECT 
    uuid_generate_v4(),
    r.tenant_id,
    r.id,
    'create',
    '',
    '',
    '',
    'Resource created during demo setup',
    'demo',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM resources r
WHERE r.name = 'demo-web-server'
ON CONFLICT DO NOTHING;

-- 创建示例同步任务
INSERT INTO sync_tasks (id, tenant_id, cloud_account_id, type, status, total_count, success_count, failure_count, started_at, completed_at, created_at, updated_at)
SELECT 
    uuid_generate_v4(),
    ca.tenant_id,
    ca.id,
    'manual',
    'completed',
    1,
    1,
    0,
    CURRENT_TIMESTAMP - INTERVAL '1 hour',
    CURRENT_TIMESTAMP - INTERVAL '59 minutes',
    CURRENT_TIMESTAMP - INTERVAL '1 hour',
    CURRENT_TIMESTAMP - INTERVAL '59 minutes'
FROM cloud_accounts ca
WHERE ca.name = 'Demo Aliyun Account'
ON CONFLICT DO NOTHING;

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE '===========================================';
    RAISE NOTICE 'CMDB 平台数据库初始化完成';
    RAISE NOTICE '===========================================';
    RAISE NOTICE '默认管理员账号:';
    RAISE NOTICE '  用户名: admin';
    RAISE NOTICE '  密码: password';
    RAISE NOTICE '  邮箱: <EMAIL>';
    RAISE NOTICE '';
    RAISE NOTICE '注意: 请在生产环境中修改默认密码！';
    RAISE NOTICE '===========================================';
END $$;
