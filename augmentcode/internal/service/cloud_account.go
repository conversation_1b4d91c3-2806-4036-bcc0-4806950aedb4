package service

import (
	"context"
	"fmt"
	"time"

	"cmdb-platform/internal/model"
	"cmdb-platform/internal/repository"
	"cmdb-platform/pkg/cloud"

	"github.com/google/uuid"
)

// CloudAccountService 云账户服务接口
type CloudAccountService interface {
	// 基础CRUD操作
	CreateCloudAccount(ctx context.Context, req *model.CreateCloudAccountRequest, userID uuid.UUID) (*model.CloudAccount, error)
	GetCloudAccount(ctx context.Context, id uuid.UUID) (*model.CloudAccount, error)
	UpdateCloudAccount(ctx context.Context, id uuid.UUID, req *model.UpdateCloudAccountRequest, userID uuid.UUID) (*model.CloudAccount, error)
	DeleteCloudAccount(ctx context.Context, id uuid.UUID, userID uuid.UUID) error
	ListCloudAccounts(ctx context.Context, query *model.CloudAccountQuery) ([]*model.CloudAccount, int64, error)

	// 连接测试和验证
	TestConnection(ctx context.Context, id uuid.UUID) (*model.ConnectionTestResult, error)
	ValidateCredentials(ctx context.Context, req *model.ValidateCredentialsRequest) (*model.ValidationResult, error)

	// DNS服务相关
	TestDNSConnection(ctx context.Context, id uuid.UUID) (*model.DNSConnectionTestResult, error)
	GetDNSCapabilities(ctx context.Context, id uuid.UUID) (*model.DNSCapabilities, error)

	// 权限和配置管理
	UpdateDNSPermissions(ctx context.Context, id uuid.UUID, permissions *model.DNSPermissions, userID uuid.UUID) error
	GetDNSPermissions(ctx context.Context, id uuid.UUID) (*model.DNSPermissions, error)
}

// cloudAccountService 云账户服务实现
type cloudAccountService struct {
	accountRepo  repository.CloudAccountRepository
	cloudManager *cloud.Manager
}

// NewCloudAccountService 创建云账户服务
func NewCloudAccountService(
	accountRepo repository.CloudAccountRepository,
	cloudManager *cloud.Manager,
) CloudAccountService {
	return &cloudAccountService{
		accountRepo:  accountRepo,
		cloudManager: cloudManager,
	}
}

// CreateCloudAccount 创建云账户
func (s *cloudAccountService) CreateCloudAccount(ctx context.Context, req *model.CreateCloudAccountRequest, userID uuid.UUID) (*model.CloudAccount, error) {
	// 验证凭据
	validateReq := &model.ValidateCredentialsRequest{
		Provider:    req.Provider,
		Region:      req.Region,
		Credentials: req.Credentials,
	}

	validationResult, err := s.ValidateCredentials(ctx, validateReq)
	if err != nil {
		return nil, fmt.Errorf("failed to validate credentials: %w", err)
	}

	if !validationResult.Valid {
		return nil, fmt.Errorf("invalid credentials: %s", validationResult.Message)
	}

	// 创建云账户记录
	account := &model.CloudAccount{
		TenantModel: model.TenantModel{
			BaseModel: model.BaseModel{
				ID:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
				CreatedBy: userID,
				UpdatedBy: userID,
			},
			TenantID: req.TenantID,
		},
		Name:        req.Name,
		Provider:    req.Provider,
		Region:      req.Region,
		Credentials: req.Credentials,
		Description: req.Description,
		Tags:        req.Tags,
		Status:      model.CloudAccountStatusActive,
	}

	if err := s.accountRepo.Create(ctx, account); err != nil {
		return nil, fmt.Errorf("failed to create cloud account: %w", err)
	}

	return account, nil
}

// GetCloudAccount 获取云账户
func (s *cloudAccountService) GetCloudAccount(ctx context.Context, id uuid.UUID) (*model.CloudAccount, error) {
	account, err := s.accountRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud account: %w", err)
	}
	if account == nil {
		return nil, fmt.Errorf("cloud account not found")
	}
	return account, nil
}

// UpdateCloudAccount 更新云账户
func (s *cloudAccountService) UpdateCloudAccount(ctx context.Context, id uuid.UUID, req *model.UpdateCloudAccountRequest, userID uuid.UUID) (*model.CloudAccount, error) {
	// 获取现有账户
	account, err := s.accountRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud account: %w", err)
	}
	if account == nil {
		return nil, fmt.Errorf("cloud account not found")
	}

	// 如果更新了凭据，需要验证
	if req.Credentials != nil {
		validateReq := &model.ValidateCredentialsRequest{
			Provider:    account.Provider,
			Region:      account.Region,
			Credentials: req.Credentials,
		}

		validationResult, err := s.ValidateCredentials(ctx, validateReq)
		if err != nil {
			return nil, fmt.Errorf("failed to validate credentials: %w", err)
		}

		if !validationResult.Valid {
			return nil, fmt.Errorf("invalid credentials: %s", validationResult.Message)
		}

		account.Credentials = req.Credentials
	}

	// 更新其他字段
	if req.Name != "" {
		account.Name = req.Name
	}
	if req.Description != "" {
		account.Description = req.Description
	}
	if req.Tags != nil {
		account.Tags = req.Tags
	}
	if req.Status != "" {
		account.Status = req.Status
	}

	account.UpdatedAt = time.Now()
	account.UpdatedBy = userID

	if err := s.accountRepo.Update(ctx, account); err != nil {
		return nil, fmt.Errorf("failed to update cloud account: %w", err)
	}

	return account, nil
}

// DeleteCloudAccount 删除云账户
func (s *cloudAccountService) DeleteCloudAccount(ctx context.Context, id uuid.UUID, userID uuid.UUID) error {
	// 检查是否有关联的DNS域名
	// TODO: 添加检查逻辑

	if err := s.accountRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete cloud account: %w", err)
	}

	return nil
}

// ListCloudAccounts 列出云账户
func (s *cloudAccountService) ListCloudAccounts(ctx context.Context, query *model.CloudAccountQuery) ([]*model.CloudAccount, int64, error) {
	// TODO: 实现分页查询逻辑
	accounts, err := s.accountRepo.List(ctx, query.TenantID)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list cloud accounts: %w", err)
	}

	// 转换为指针切片
	var accountPtrs []*model.CloudAccount
	for i := range accounts {
		accountPtrs = append(accountPtrs, &accounts[i])
	}

	return accountPtrs, int64(len(accounts)), nil
}

// TestConnection 测试连接
func (s *cloudAccountService) TestConnection(ctx context.Context, id uuid.UUID) (*model.ConnectionTestResult, error) {
	account, err := s.accountRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud account: %w", err)
	}
	if account == nil {
		return nil, fmt.Errorf("cloud account not found")
	}

	// 获取云服务提供商
	provider, err := s.cloudManager.GetProvider(account.Provider)
	if err != nil {
		return &model.ConnectionTestResult{
			Success: false,
			Message: fmt.Sprintf("Failed to get cloud provider: %v", err),
		}, nil
	}

	// 测试基础连接（例如列出实例）
	_, err = provider.ListInstances(ctx, account.Region)
	if err != nil {
		return &model.ConnectionTestResult{
			Success: false,
			Message: fmt.Sprintf("Connection test failed: %v", err),
		}, nil
	}

	return &model.ConnectionTestResult{
		Success: true,
		Message: "Connection test successful",
	}, nil
}

// ValidateCredentials 验证凭据
func (s *cloudAccountService) ValidateCredentials(ctx context.Context, req *model.ValidateCredentialsRequest) (*model.ValidationResult, error) {
	// 创建临时云服务提供商实例进行验证
	var provider cloud.Provider
	var err error

	switch req.Provider {
	case model.CloudProviderAliyun:
		accessKeyID, ok := req.Credentials["access_key_id"].(string)
		if !ok {
			return &model.ValidationResult{
				Valid:   false,
				Message: "access_key_id is required for Aliyun",
			}, nil
		}
		accessKeySecret, ok := req.Credentials["access_key_secret"].(string)
		if !ok {
			return &model.ValidationResult{
				Valid:   false,
				Message: "access_key_secret is required for Aliyun",
			}, nil
		}
		provider, err = s.cloudManager.CreateAliyunProvider(accessKeyID, accessKeySecret, req.Region)
	case model.CloudProviderAWS:
		accessKeyID, ok := req.Credentials["access_key_id"].(string)
		if !ok {
			return &model.ValidationResult{
				Valid:   false,
				Message: "access_key_id is required for AWS",
			}, nil
		}
		secretAccessKey, ok := req.Credentials["secret_access_key"].(string)
		if !ok {
			return &model.ValidationResult{
				Valid:   false,
				Message: "secret_access_key is required for AWS",
			}, nil
		}
		provider, err = s.cloudManager.CreateAWSProvider(accessKeyID, secretAccessKey, req.Region)
	case model.CloudProviderTencent:
		secretID, ok := req.Credentials["secret_id"].(string)
		if !ok {
			return &model.ValidationResult{
				Valid:   false,
				Message: "secret_id is required for Tencent Cloud",
			}, nil
		}
		secretKey, ok := req.Credentials["secret_key"].(string)
		if !ok {
			return &model.ValidationResult{
				Valid:   false,
				Message: "secret_key is required for Tencent Cloud",
			}, nil
		}
		provider, err = s.cloudManager.CreateTencentProvider(secretID, secretKey, req.Region)
	default:
		return &model.ValidationResult{
			Valid:   false,
			Message: fmt.Sprintf("Unsupported provider: %s", req.Provider),
		}, nil
	}

	if err != nil {
		return &model.ValidationResult{
			Valid:   false,
			Message: fmt.Sprintf("Failed to create provider: %v", err),
		}, nil
	}

	// 测试连接
	_, err = provider.ListInstances(ctx, req.Region)
	if err != nil {
		return &model.ValidationResult{
			Valid:   false,
			Message: fmt.Sprintf("Credential validation failed: %v", err),
		}, nil
	}

	return &model.ValidationResult{
		Valid:   true,
		Message: "Credentials are valid",
	}, nil
}

// TestDNSConnection 测试DNS连接
func (s *cloudAccountService) TestDNSConnection(ctx context.Context, id uuid.UUID) (*model.DNSConnectionTestResult, error) {
	account, err := s.accountRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud account: %w", err)
	}
	if account == nil {
		return nil, fmt.Errorf("cloud account not found")
	}

	// 获取云服务提供商
	provider, err := s.cloudManager.GetProvider(account.Provider)
	if err != nil {
		return &model.DNSConnectionTestResult{
			Success: false,
			Message: fmt.Sprintf("Failed to get cloud provider: %v", err),
		}, nil
	}

	dnsProvider := provider.DNSProvider()

	// 测试DNS连接（列出DNS域名）
	zones, err := dnsProvider.ListDNSZones(ctx)
	if err != nil {
		return &model.DNSConnectionTestResult{
			Success: false,
			Message: fmt.Sprintf("DNS connection test failed: %v", err),
		}, nil
	}

	return &model.DNSConnectionTestResult{
		Success:   true,
		Message:   "DNS connection test successful",
		ZoneCount: len(zones),
	}, nil
}

// GetDNSCapabilities 获取DNS能力
func (s *cloudAccountService) GetDNSCapabilities(ctx context.Context, id uuid.UUID) (*model.DNSCapabilities, error) {
	account, err := s.accountRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud account: %w", err)
	}
	if account == nil {
		return nil, fmt.Errorf("cloud account not found")
	}

	capabilities := &model.DNSCapabilities{
		Provider: account.Provider,
	}

	switch account.Provider {
	case model.CloudProviderAliyun:
		capabilities.SupportedRecordTypes = []string{"A", "AAAA", "CNAME", "MX", "TXT", "NS", "SRV", "CAA"}
		capabilities.MaxZones = 100
		capabilities.MaxRecordsPerZone = 10000
		capabilities.SupportsBatchOperations = true
		capabilities.SupportsHealthChecks = false
		capabilities.SupportsGeoDNS = true
		capabilities.MinTTL = 1
		capabilities.MaxTTL = 86400
	case model.CloudProviderAWS:
		capabilities.SupportedRecordTypes = []string{"A", "AAAA", "CNAME", "MX", "TXT", "NS", "SOA", "SRV", "PTR", "CAA"}
		capabilities.MaxZones = 500
		capabilities.MaxRecordsPerZone = 10000
		capabilities.SupportsBatchOperations = true
		capabilities.SupportsHealthChecks = true
		capabilities.SupportsGeoDNS = true
		capabilities.MinTTL = 1
		capabilities.MaxTTL = **********
	case model.CloudProviderTencent:
		capabilities.SupportedRecordTypes = []string{"A", "AAAA", "CNAME", "MX", "TXT", "NS", "SRV"}
		capabilities.MaxZones = 100
		capabilities.MaxRecordsPerZone = 5000
		capabilities.SupportsBatchOperations = false
		capabilities.SupportsHealthChecks = false
		capabilities.SupportsGeoDNS = false
		capabilities.MinTTL = 1
		capabilities.MaxTTL = 604800
	}

	return capabilities, nil
}

// UpdateDNSPermissions 更新DNS权限
func (s *cloudAccountService) UpdateDNSPermissions(ctx context.Context, id uuid.UUID, permissions *model.DNSPermissions, userID uuid.UUID) error {
	account, err := s.accountRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get cloud account: %w", err)
	}
	if account == nil {
		return fmt.Errorf("cloud account not found")
	}

	// 更新DNS权限配置
	if account.DNSConfig == nil {
		account.DNSConfig = make(map[string]interface{})
	}

	account.DNSConfig["permissions"] = permissions
	account.UpdatedAt = time.Now()
	account.UpdatedBy = userID

	if err := s.accountRepo.Update(ctx, account); err != nil {
		return fmt.Errorf("failed to update DNS permissions: %w", err)
	}

	return nil
}

// GetDNSPermissions 获取DNS权限
func (s *cloudAccountService) GetDNSPermissions(ctx context.Context, id uuid.UUID) (*model.DNSPermissions, error) {
	account, err := s.accountRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud account: %w", err)
	}
	if account == nil {
		return nil, fmt.Errorf("cloud account not found")
	}

	if account.DNSConfig == nil {
		// 返回默认权限
		return &model.DNSPermissions{
			CanCreateZones:   true,
			CanDeleteZones:   true,
			CanCreateRecords: true,
			CanDeleteRecords: true,
			CanModifyRecords: true,
			AllowedZones:     []string{},
			RestrictedZones:  []string{},
		}, nil
	}

	if permissions, ok := account.DNSConfig["permissions"].(*model.DNSPermissions); ok {
		return permissions, nil
	}

	// 返回默认权限
	return &model.DNSPermissions{
		CanCreateZones:   true,
		CanDeleteZones:   true,
		CanCreateRecords: true,
		CanDeleteRecords: true,
		CanModifyRecords: true,
		AllowedZones:     []string{},
		RestrictedZones:  []string{},
	}, nil
}
