package service

import (
	"context"
	"fmt"
	"time"

	"cmdb-platform/internal/model"
	"cmdb-platform/internal/repository"
	"cmdb-platform/pkg/cloud"
	"cmdb-platform/pkg/logger"
	
	"github.com/google/uuid"
)

// ResourceService 资源服务接口
type ResourceService interface {
	CreateResource(ctx context.Context, req *model.CreateResourceRequest) (*model.Resource, error)
	UpdateResource(ctx context.Context, id uuid.UUID, req *model.UpdateResourceRequest) (*model.Resource, error)
	DeleteResource(ctx context.Context, id uuid.UUID) error
	GetResource(ctx context.Context, id uuid.UUID) (*model.Resource, error)
	ListResources(ctx context.Context, query *model.ResourceQuery) (*model.ListResponse, error)
	SyncResources(ctx context.Context, req *model.SyncTaskRequest) (*model.SyncTask, error)
	GetResourceStats(ctx context.Context, tenantID uuid.UUID) (*model.StatsResponse, error)
}

// resourceService 资源服务实现
type resourceService struct {
	resourceRepo     repository.ResourceRepository
	cloudAccountRepo repository.CloudAccountRepository
	syncTaskRepo     repository.SyncTaskRepository
	cloudManager     *cloud.Manager
}

// NewResourceService 创建资源服务
func NewResourceService(
	resourceRepo repository.ResourceRepository,
	cloudAccountRepo repository.CloudAccountRepository,
	syncTaskRepo repository.SyncTaskRepository,
	cloudManager *cloud.Manager,
) ResourceService {
	return &resourceService{
		resourceRepo:     resourceRepo,
		cloudAccountRepo: cloudAccountRepo,
		syncTaskRepo:     syncTaskRepo,
		cloudManager:     cloudManager,
	}
}

// CreateResource 创建资源
func (s *resourceService) CreateResource(ctx context.Context, req *model.CreateResourceRequest) (*model.Resource, error) {
	now := time.Now()
	
	resource := &model.Resource{
		TenantModel: model.TenantModel{
			BaseModel: model.BaseModel{
				ID:        uuid.New(),
				CreatedAt: now,
				UpdatedAt: now,
			},
			// TenantID 需要从上下文中获取
		},
		CloudAccountID: req.CloudAccountID,
		Type:           req.Type,
		Name:           req.Name,
		CloudID:        req.CloudID,
		Region:         req.Region,
		Zone:           req.Zone,
		Status:         req.Status,
		Tags:           req.Tags,
		Properties:     req.Properties,
		Metadata:       req.Metadata,
		LastSyncAt:     &now,
	}
	
	// 获取云账号信息
	cloudAccount, err := s.cloudAccountRepo.GetByID(ctx, req.CloudAccountID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud account: %w", err)
	}
	if cloudAccount == nil {
		return nil, fmt.Errorf("cloud account not found")
	}
	
	resource.Provider = cloudAccount.Provider
	resource.TenantID = cloudAccount.TenantID
	
	if err := s.resourceRepo.Create(ctx, resource); err != nil {
		return nil, fmt.Errorf("failed to create resource: %w", err)
	}
	
	logger.Infof("Resource created successfully: %s", resource.ID)
	return resource, nil
}

// UpdateResource 更新资源
func (s *resourceService) UpdateResource(ctx context.Context, id uuid.UUID, req *model.UpdateResourceRequest) (*model.Resource, error) {
	resource, err := s.resourceRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource: %w", err)
	}
	if resource == nil {
		return nil, fmt.Errorf("resource not found")
	}
	
	// 更新字段
	if req.Name != "" {
		resource.Name = req.Name
	}
	if req.Status != "" {
		resource.Status = req.Status
	}
	if req.Tags != nil {
		resource.Tags = req.Tags
	}
	if req.Properties != nil {
		resource.Properties = req.Properties
	}
	if req.Metadata != nil {
		resource.Metadata = req.Metadata
	}
	
	resource.UpdatedAt = time.Now()
	
	if err := s.resourceRepo.Update(ctx, resource); err != nil {
		return nil, fmt.Errorf("failed to update resource: %w", err)
	}
	
	logger.Infof("Resource updated successfully: %s", resource.ID)
	return resource, nil
}

// DeleteResource 删除资源
func (s *resourceService) DeleteResource(ctx context.Context, id uuid.UUID) error {
	resource, err := s.resourceRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get resource: %w", err)
	}
	if resource == nil {
		return fmt.Errorf("resource not found")
	}
	
	if err := s.resourceRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete resource: %w", err)
	}
	
	logger.Infof("Resource deleted successfully: %s", id)
	return nil
}

// GetResource 获取资源
func (s *resourceService) GetResource(ctx context.Context, id uuid.UUID) (*model.Resource, error) {
	resource, err := s.resourceRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource: %w", err)
	}
	if resource == nil {
		return nil, fmt.Errorf("resource not found")
	}
	
	return resource, nil
}

// ListResources 列出资源
func (s *resourceService) ListResources(ctx context.Context, query *model.ResourceQuery) (*model.ListResponse, error) {
	// 设置默认值
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.PageSize <= 0 {
		query.PageSize = 20
	}
	if query.PageSize > 100 {
		query.PageSize = 100
	}
	if query.SortBy == "" {
		query.SortBy = "created_at"
	}
	if query.SortOrder == "" {
		query.SortOrder = "desc"
	}
	
	resources, total, err := s.resourceRepo.List(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to list resources: %w", err)
	}
	
	totalPage := int(total) / query.PageSize
	if int(total)%query.PageSize > 0 {
		totalPage++
	}
	
	return &model.ListResponse{
		Data: resources,
		Pagination: model.PaginationResponse{
			Page:      query.Page,
			PageSize:  query.PageSize,
			Total:     total,
			TotalPage: totalPage,
		},
	}, nil
}

// SyncResources 同步资源
func (s *resourceService) SyncResources(ctx context.Context, req *model.SyncTaskRequest) (*model.SyncTask, error) {
	// 获取云账号信息
	cloudAccount, err := s.cloudAccountRepo.GetByID(ctx, req.CloudAccountID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud account: %w", err)
	}
	if cloudAccount == nil {
		return nil, fmt.Errorf("cloud account not found")
	}
	
	// 创建同步任务
	now := time.Now()
	syncTask := &model.SyncTask{
		TenantModel: model.TenantModel{
			BaseModel: model.BaseModel{
				ID:        uuid.New(),
				CreatedAt: now,
				UpdatedAt: now,
			},
			TenantID: cloudAccount.TenantID,
		},
		CloudAccountID: req.CloudAccountID,
		Type:           req.Type,
		Status:         model.SyncStatusPending,
		StartedAt:      &now,
	}
	
	if err := s.syncTaskRepo.Create(ctx, syncTask); err != nil {
		return nil, fmt.Errorf("failed to create sync task: %w", err)
	}
	
	// 异步执行同步
	go s.performSync(context.Background(), syncTask, cloudAccount, req.ResourceTypes)
	
	logger.Infof("Sync task created: %s", syncTask.ID)
	return syncTask, nil
}

// performSync 执行同步
func (s *resourceService) performSync(ctx context.Context, syncTask *model.SyncTask, cloudAccount *model.CloudAccount, resourceTypes []model.ResourceType) {
	// 更新任务状态为运行中
	syncTask.Status = model.SyncStatusRunning
	s.syncTaskRepo.Update(ctx, syncTask)
	
	// 执行云资源同步
	result, err := s.cloudManager.SyncResources(ctx, cloudAccount, resourceTypes)
	if err != nil {
		logger.Errorf("Failed to sync resources: %v", err)
		syncTask.Status = model.SyncStatusFailed
		syncTask.ErrorMessage = err.Error()
	} else {
		// 保存同步的资源
		if len(result.Resources) > 0 {
			if err := s.saveOrUpdateResources(ctx, result.Resources); err != nil {
				logger.Errorf("Failed to save synced resources: %v", err)
				syncTask.Status = model.SyncStatusFailed
				syncTask.ErrorMessage = err.Error()
			} else {
				syncTask.Status = model.SyncStatusCompleted
			}
		} else {
			syncTask.Status = model.SyncStatusCompleted
		}
		
		syncTask.TotalCount = result.TotalCount
		syncTask.SuccessCount = result.SuccessCount
		syncTask.FailureCount = result.FailureCount
	}
	
	// 更新任务完成时间
	now := time.Now()
	syncTask.CompletedAt = &now
	syncTask.UpdatedAt = now
	
	// 更新云账号最后同步时间
	cloudAccount.LastSyncAt = &now
	s.cloudAccountRepo.Update(ctx, cloudAccount)
	
	// 保存任务状态
	s.syncTaskRepo.Update(ctx, syncTask)
	
	logger.Infof("Sync task completed: %s, status: %s", syncTask.ID, syncTask.Status)
}

// saveOrUpdateResources 保存或更新资源
func (s *resourceService) saveOrUpdateResources(ctx context.Context, resources []model.Resource) error {
	var newResources []model.Resource
	var updateResources []model.Resource
	
	for _, resource := range resources {
		// 检查资源是否已存在
		existing, err := s.resourceRepo.GetByCloudID(ctx, resource.CloudAccountID, resource.CloudID)
		if err != nil {
			return err
		}
		
		if existing == nil {
			// 新资源
			newResources = append(newResources, resource)
		} else {
			// 更新现有资源
			existing.Name = resource.Name
			existing.Status = resource.Status
			existing.Tags = resource.Tags
			existing.Properties = resource.Properties
			existing.Metadata = resource.Metadata
			existing.LastSyncAt = resource.LastSyncAt
			existing.UpdatedAt = time.Now()
			updateResources = append(updateResources, *existing)
		}
	}
	
	// 批量创建新资源
	if len(newResources) > 0 {
		if err := s.resourceRepo.BatchCreate(ctx, newResources); err != nil {
			return fmt.Errorf("failed to batch create resources: %w", err)
		}
		logger.Infof("Created %d new resources", len(newResources))
	}
	
	// 批量更新现有资源
	if len(updateResources) > 0 {
		if err := s.resourceRepo.BatchUpdate(ctx, updateResources); err != nil {
			return fmt.Errorf("failed to batch update resources: %w", err)
		}
		logger.Infof("Updated %d existing resources", len(updateResources))
	}
	
	return nil
}

// GetResourceStats 获取资源统计
func (s *resourceService) GetResourceStats(ctx context.Context, tenantID uuid.UUID) (*model.StatsResponse, error) {
	stats, err := s.resourceRepo.GetStats(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource stats: %w", err)
	}
	
	return stats, nil
}
