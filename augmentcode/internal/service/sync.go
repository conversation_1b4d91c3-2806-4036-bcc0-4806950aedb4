package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"cmdb-platform/internal/config"
	"cmdb-platform/internal/model"
	"cmdb-platform/internal/repository"
	"cmdb-platform/pkg/cloud"
	"cmdb-platform/pkg/logger"
	
	"github.com/google/uuid"
	"github.com/robfig/cron/v3"
)

// SyncService 同步服务接口
type SyncService interface {
	Start() error
	Stop() error
	SyncCloudAccount(ctx context.Context, cloudAccountID uuid.UUID) error
	SyncAllCloudAccounts(ctx context.Context) error
	GetSyncStatus() map[uuid.UUID]SyncStatus
}

// SyncStatus 同步状态
type SyncStatus struct {
	CloudAccountID uuid.UUID `json:"cloud_account_id"`
	Status         string    `json:"status"`
	LastSyncAt     time.Time `json:"last_sync_at"`
	NextSyncAt     time.Time `json:"next_sync_at"`
	Error          string    `json:"error,omitempty"`
}

// syncService 同步服务实现
type syncService struct {
	config             *config.Config
	cloudAccountRepo   repository.CloudAccountRepository
	resourceRepo       repository.ResourceRepository
	syncTaskRepo       repository.SyncTaskRepository
	changeRecordRepo   repository.ChangeRecordRepository
	cloudManager       *cloud.Manager
	cron               *cron.Cron
	syncStatuses       map[uuid.UUID]SyncStatus
	syncMutex          sync.RWMutex
	running            bool
}

// NewSyncService 创建同步服务
func NewSyncService(
	cfg *config.Config,
	cloudAccountRepo repository.CloudAccountRepository,
	resourceRepo repository.ResourceRepository,
	syncTaskRepo repository.SyncTaskRepository,
	changeRecordRepo repository.ChangeRecordRepository,
	cloudManager *cloud.Manager,
) SyncService {
	return &syncService{
		config:           cfg,
		cloudAccountRepo: cloudAccountRepo,
		resourceRepo:     resourceRepo,
		syncTaskRepo:     syncTaskRepo,
		changeRecordRepo: changeRecordRepo,
		cloudManager:     cloudManager,
		cron:             cron.New(),
		syncStatuses:     make(map[uuid.UUID]SyncStatus),
	}
}

// Start 启动同步服务
func (s *syncService) Start() error {
	if s.running {
		return fmt.Errorf("sync service is already running")
	}
	
	if !s.config.Sync.Enabled {
		logger.Info("Sync service is disabled")
		return nil
	}
	
	// 添加定时任务
	_, err := s.cron.AddFunc(fmt.Sprintf("@every %s", s.config.Sync.Interval), func() {
		ctx := context.Background()
		if err := s.SyncAllCloudAccounts(ctx); err != nil {
			logger.Errorf("Scheduled sync failed: %v", err)
		}
	})
	if err != nil {
		return fmt.Errorf("failed to add cron job: %w", err)
	}
	
	s.cron.Start()
	s.running = true
	
	logger.Infof("Sync service started with interval: %s", s.config.Sync.Interval)
	return nil
}

// Stop 停止同步服务
func (s *syncService) Stop() error {
	if !s.running {
		return nil
	}
	
	s.cron.Stop()
	s.running = false
	
	logger.Info("Sync service stopped")
	return nil
}

// SyncCloudAccount 同步单个云账号
func (s *syncService) SyncCloudAccount(ctx context.Context, cloudAccountID uuid.UUID) error {
	// 获取云账号信息
	cloudAccount, err := s.cloudAccountRepo.GetByID(ctx, cloudAccountID)
	if err != nil {
		return fmt.Errorf("failed to get cloud account: %w", err)
	}
	if cloudAccount == nil {
		return fmt.Errorf("cloud account not found")
	}
	
	// 更新同步状态
	s.updateSyncStatus(cloudAccountID, "running", time.Now(), time.Time{}, "")
	
	// 创建同步任务
	syncTask := &model.SyncTask{
		TenantModel: model.TenantModel{
			BaseModel: model.BaseModel{
				ID:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			TenantID: cloudAccount.TenantID,
		},
		CloudAccountID: cloudAccountID,
		Type:           "scheduled",
		Status:         model.SyncStatusRunning,
		StartedAt:      &[]time.Time{time.Now()}[0],
	}
	
	if err := s.syncTaskRepo.Create(ctx, syncTask); err != nil {
		logger.Errorf("Failed to create sync task: %v", err)
	}
	
	// 执行同步
	result, err := s.cloudManager.SyncResources(ctx, cloudAccount, nil)
	if err != nil {
		// 更新同步状态为失败
		s.updateSyncStatus(cloudAccountID, "failed", time.Now(), s.getNextSyncTime(), err.Error())
		
		// 更新同步任务状态
		syncTask.Status = model.SyncStatusFailed
		syncTask.ErrorMessage = err.Error()
		syncTask.CompletedAt = &[]time.Time{time.Now()}[0]
		s.syncTaskRepo.Update(ctx, syncTask)
		
		return fmt.Errorf("sync failed: %w", err)
	}
	
	// 保存同步结果
	if err := s.processSyncResult(ctx, cloudAccount, result); err != nil {
		logger.Errorf("Failed to process sync result: %v", err)
		s.updateSyncStatus(cloudAccountID, "failed", time.Now(), s.getNextSyncTime(), err.Error())
		
		syncTask.Status = model.SyncStatusFailed
		syncTask.ErrorMessage = err.Error()
	} else {
		s.updateSyncStatus(cloudAccountID, "completed", time.Now(), s.getNextSyncTime(), "")
		syncTask.Status = model.SyncStatusCompleted
	}
	
	// 更新同步任务
	syncTask.TotalCount = result.TotalCount
	syncTask.SuccessCount = result.SuccessCount
	syncTask.FailureCount = result.FailureCount
	syncTask.CompletedAt = &[]time.Time{time.Now()}[0]
	s.syncTaskRepo.Update(ctx, syncTask)
	
	// 更新云账号最后同步时间
	now := time.Now()
	cloudAccount.LastSyncAt = &now
	s.cloudAccountRepo.Update(ctx, cloudAccount)
	
	logger.Infof("Cloud account %s sync completed: %d resources processed", cloudAccount.Name, result.TotalCount)
	return nil
}

// SyncAllCloudAccounts 同步所有云账号
func (s *syncService) SyncAllCloudAccounts(ctx context.Context) error {
	logger.Info("Starting scheduled sync for all cloud accounts")
	
	// 获取所有启用的云账号
	providers := s.cloudManager.ListProviders()
	var allAccounts []model.CloudAccount
	
	for _, provider := range providers {
		accounts, err := s.cloudAccountRepo.ListByProvider(ctx, provider)
		if err != nil {
			logger.Errorf("Failed to get accounts for provider %s: %v", provider, err)
			continue
		}
		allAccounts = append(allAccounts, accounts...)
	}
	
	if len(allAccounts) == 0 {
		logger.Info("No cloud accounts found for sync")
		return nil
	}
	
	// 并发同步（限制并发数）
	semaphore := make(chan struct{}, 3) // 最多3个并发同步
	var wg sync.WaitGroup
	
	for _, account := range allAccounts {
		if account.Status != "active" {
			continue
		}
		
		wg.Add(1)
		go func(acc model.CloudAccount) {
			defer wg.Done()
			
			semaphore <- struct{}{} // 获取信号量
			defer func() { <-semaphore }() // 释放信号量
			
			if err := s.SyncCloudAccount(ctx, acc.ID); err != nil {
				logger.Errorf("Failed to sync cloud account %s: %v", acc.Name, err)
			}
		}(account)
	}
	
	wg.Wait()
	logger.Info("Scheduled sync for all cloud accounts completed")
	return nil
}

// GetSyncStatus 获取同步状态
func (s *syncService) GetSyncStatus() map[uuid.UUID]SyncStatus {
	s.syncMutex.RLock()
	defer s.syncMutex.RUnlock()
	
	result := make(map[uuid.UUID]SyncStatus)
	for k, v := range s.syncStatuses {
		result[k] = v
	}
	return result
}

// updateSyncStatus 更新同步状态
func (s *syncService) updateSyncStatus(cloudAccountID uuid.UUID, status string, lastSyncAt, nextSyncAt time.Time, errorMsg string) {
	s.syncMutex.Lock()
	defer s.syncMutex.Unlock()
	
	s.syncStatuses[cloudAccountID] = SyncStatus{
		CloudAccountID: cloudAccountID,
		Status:         status,
		LastSyncAt:     lastSyncAt,
		NextSyncAt:     nextSyncAt,
		Error:          errorMsg,
	}
}

// getNextSyncTime 获取下次同步时间
func (s *syncService) getNextSyncTime() time.Time {
	return time.Now().Add(s.config.Sync.Interval)
}

// processSyncResult 处理同步结果
func (s *syncService) processSyncResult(ctx context.Context, cloudAccount *model.CloudAccount, result *cloud.SyncResult) error {
	if len(result.Resources) == 0 {
		return nil
	}
	
	// 获取现有资源
	existingResources, err := s.resourceRepo.ListByCloudAccount(ctx, cloudAccount.ID)
	if err != nil {
		return fmt.Errorf("failed to get existing resources: %w", err)
	}
	
	// 创建现有资源映射
	existingMap := make(map[string]*model.Resource)
	for i := range existingResources {
		existingMap[existingResources[i].CloudID] = &existingResources[i]
	}
	
	var newResources []model.Resource
	var updatedResources []model.Resource
	var changeRecords []model.ChangeRecord
	
	// 处理同步的资源
	for _, resource := range result.Resources {
		existing, exists := existingMap[resource.CloudID]
		
		if !exists {
			// 新资源
			newResources = append(newResources, resource)
			
			// 记录变更
			changeRecords = append(changeRecords, model.ChangeRecord{
				TenantModel: model.TenantModel{
					BaseModel: model.BaseModel{
						ID:        uuid.New(),
						CreatedAt: time.Now(),
						UpdatedAt: time.Now(),
					},
					TenantID: resource.TenantID,
				},
				ResourceID:  resource.ID,
				Type:        model.ChangeTypeCreate,
				Description: fmt.Sprintf("Resource %s created", resource.Name),
				Source:      "sync",
			})
		} else {
			// 检查是否有变更
			if s.hasResourceChanged(existing, &resource) {
				// 记录变更
				changeRecords = append(changeRecords, s.createChangeRecords(existing, &resource)...)
				
				// 更新资源
				existing.Name = resource.Name
				existing.Status = resource.Status
				existing.Tags = resource.Tags
				existing.Properties = resource.Properties
				existing.Metadata = resource.Metadata
				existing.LastSyncAt = resource.LastSyncAt
				existing.UpdatedAt = time.Now()
				
				updatedResources = append(updatedResources, *existing)
			}
			
			// 从映射中移除，剩下的就是已删除的资源
			delete(existingMap, resource.CloudID)
		}
	}
	
	// 处理已删除的资源
	for _, deletedResource := range existingMap {
		// 标记为已删除
		deletedResource.Status = model.ResourceStatusDeleted
		deletedResource.UpdatedAt = time.Now()
		updatedResources = append(updatedResources, *deletedResource)
		
		// 记录变更
		changeRecords = append(changeRecords, model.ChangeRecord{
			TenantModel: model.TenantModel{
				BaseModel: model.BaseModel{
					ID:        uuid.New(),
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				},
				TenantID: deletedResource.TenantID,
			},
			ResourceID:  deletedResource.ID,
			Type:        model.ChangeTypeDelete,
			Description: fmt.Sprintf("Resource %s deleted", deletedResource.Name),
			Source:      "sync",
		})
	}
	
	// 批量保存
	if len(newResources) > 0 {
		if err := s.resourceRepo.BatchCreate(ctx, newResources); err != nil {
			return fmt.Errorf("failed to create new resources: %w", err)
		}
		logger.Infof("Created %d new resources", len(newResources))
	}
	
	if len(updatedResources) > 0 {
		if err := s.resourceRepo.BatchUpdate(ctx, updatedResources); err != nil {
			return fmt.Errorf("failed to update resources: %w", err)
		}
		logger.Infof("Updated %d resources", len(updatedResources))
	}
	
	if len(changeRecords) > 0 {
		if err := s.changeRecordRepo.BatchCreate(ctx, changeRecords); err != nil {
			logger.Errorf("Failed to create change records: %v", err)
		} else {
			logger.Infof("Created %d change records", len(changeRecords))
		}
	}
	
	return nil
}

// hasResourceChanged 检查资源是否有变更
func (s *syncService) hasResourceChanged(existing, new *model.Resource) bool {
	return existing.Name != new.Name ||
		existing.Status != new.Status ||
		!s.compareJSON(existing.Tags, new.Tags) ||
		!s.compareJSON(existing.Properties, new.Properties)
}

// compareJSON 比较JSON字段
func (s *syncService) compareJSON(a, b interface{}) bool {
	// 简化实现，实际应该进行深度比较
	return fmt.Sprintf("%v", a) == fmt.Sprintf("%v", b)
}

// createChangeRecords 创建变更记录
func (s *syncService) createChangeRecords(existing, new *model.Resource) []model.ChangeRecord {
	var records []model.ChangeRecord
	now := time.Now()
	
	if existing.Name != new.Name {
		records = append(records, model.ChangeRecord{
			TenantModel: model.TenantModel{
				BaseModel: model.BaseModel{
					ID:        uuid.New(),
					CreatedAt: now,
					UpdatedAt: now,
				},
				TenantID: existing.TenantID,
			},
			ResourceID:  existing.ID,
			Type:        model.ChangeTypeUpdate,
			Field:       "name",
			OldValue:    existing.Name,
			NewValue:    new.Name,
			Description: fmt.Sprintf("Name changed from %s to %s", existing.Name, new.Name),
			Source:      "sync",
		})
	}
	
	if existing.Status != new.Status {
		records = append(records, model.ChangeRecord{
			TenantModel: model.TenantModel{
				BaseModel: model.BaseModel{
					ID:        uuid.New(),
					CreatedAt: now,
					UpdatedAt: now,
				},
				TenantID: existing.TenantID,
			},
			ResourceID:  existing.ID,
			Type:        model.ChangeTypeUpdate,
			Field:       "status",
			OldValue:    string(existing.Status),
			NewValue:    string(new.Status),
			Description: fmt.Sprintf("Status changed from %s to %s", existing.Status, new.Status),
			Source:      "sync",
		})
	}
	
	return records
}
