package service

import (
	"context"
	"fmt"
	"time"

	"cmdb-platform/internal/config"
	"cmdb-platform/internal/model"
	"cmdb-platform/internal/repository"
	"cmdb-platform/pkg/logger"
	
	"github.com/google/uuid"
)

// AlertService 告警服务接口
type AlertService interface {
	CreateAlert(ctx context.Context, req *model.CreateAlertRequest) (*model.Alert, error)
	UpdateAlert(ctx context.Context, id uuid.UUID, updates map[string]interface{}) (*model.Alert, error)
	ResolveAlert(ctx context.Context, id uuid.UUID) error
	GetAlert(ctx context.Context, id uuid.UUID) (*model.Alert, error)
	ListAlerts(ctx context.Context, query *model.AlertQuery) (*model.ListResponse, error)
	ProcessResourceChange(ctx context.Context, resource *model.Resource, changeType model.ChangeType) error
	CheckResourceHealth(ctx context.Context, resourceID uuid.UUID) error
}

// alertService 告警服务实现
type alertService struct {
	config       *config.Config
	alertRepo    repository.AlertRepository
	resourceRepo repository.ResourceRepository
	notifier     AlertNotifier
}

// AlertNotifier 告警通知器接口
type AlertNotifier interface {
	SendAlert(ctx context.Context, alert *model.Alert) error
}

// NewAlertService 创建告警服务
func NewAlertService(
	cfg *config.Config,
	alertRepo repository.AlertRepository,
	resourceRepo repository.ResourceRepository,
	notifier AlertNotifier,
) AlertService {
	return &alertService{
		config:       cfg,
		alertRepo:    alertRepo,
		resourceRepo: resourceRepo,
		notifier:     notifier,
	}
}

// CreateAlert 创建告警
func (s *alertService) CreateAlert(ctx context.Context, req *model.CreateAlertRequest) (*model.Alert, error) {
	now := time.Now()
	
	alert := &model.Alert{
		TenantModel: model.TenantModel{
			BaseModel: model.BaseModel{
				ID:        uuid.New(),
				CreatedAt: now,
				UpdatedAt: now,
			},
			// TenantID 需要从上下文中获取
		},
		ResourceID:  req.ResourceID,
		Title:       req.Title,
		Description: req.Description,
		Level:       req.Level,
		Status:      model.AlertStatusOpen,
		Source:      req.Source,
		RuleID:      req.RuleID,
		Tags:        req.Tags,
	}
	
	// 如果有资源ID，获取资源信息设置租户ID
	if req.ResourceID != uuid.Nil {
		resource, err := s.resourceRepo.GetByID(ctx, req.ResourceID)
		if err != nil {
			return nil, fmt.Errorf("failed to get resource: %w", err)
		}
		if resource != nil {
			alert.TenantID = resource.TenantID
		}
	}
	
	if err := s.alertRepo.Create(ctx, alert); err != nil {
		return nil, fmt.Errorf("failed to create alert: %w", err)
	}
	
	// 发送通知
	if s.config.Alert.Enabled && s.notifier != nil {
		go func() {
			if err := s.notifier.SendAlert(context.Background(), alert); err != nil {
				logger.Errorf("Failed to send alert notification: %v", err)
			}
		}()
	}
	
	logger.Infof("Alert created: %s", alert.Title)
	return alert, nil
}

// UpdateAlert 更新告警
func (s *alertService) UpdateAlert(ctx context.Context, id uuid.UUID, updates map[string]interface{}) (*model.Alert, error) {
	alert, err := s.alertRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get alert: %w", err)
	}
	if alert == nil {
		return nil, fmt.Errorf("alert not found")
	}
	
	// 应用更新
	if title, ok := updates["title"]; ok {
		alert.Title = title.(string)
	}
	if description, ok := updates["description"]; ok {
		alert.Description = description.(string)
	}
	if level, ok := updates["level"]; ok {
		alert.Level = level.(model.AlertLevel)
	}
	if status, ok := updates["status"]; ok {
		alert.Status = status.(model.AlertStatus)
		if alert.Status == model.AlertStatusResolved {
			now := time.Now()
			alert.ResolvedAt = &now
		}
	}
	if tags, ok := updates["tags"]; ok {
		alert.Tags = tags.(map[string]interface{})
	}
	
	alert.UpdatedAt = time.Now()
	
	if err := s.alertRepo.Update(ctx, alert); err != nil {
		return nil, fmt.Errorf("failed to update alert: %w", err)
	}
	
	logger.Infof("Alert updated: %s", alert.Title)
	return alert, nil
}

// ResolveAlert 解决告警
func (s *alertService) ResolveAlert(ctx context.Context, id uuid.UUID) error {
	updates := map[string]interface{}{
		"status": model.AlertStatusResolved,
	}
	
	_, err := s.UpdateAlert(ctx, id, updates)
	if err != nil {
		return fmt.Errorf("failed to resolve alert: %w", err)
	}
	
	logger.Infof("Alert resolved: %s", id)
	return nil
}

// GetAlert 获取告警
func (s *alertService) GetAlert(ctx context.Context, id uuid.UUID) (*model.Alert, error) {
	alert, err := s.alertRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get alert: %w", err)
	}
	if alert == nil {
		return nil, fmt.Errorf("alert not found")
	}
	
	return alert, nil
}

// ListAlerts 列出告警
func (s *alertService) ListAlerts(ctx context.Context, query *model.AlertQuery) (*model.ListResponse, error) {
	// 设置默认值
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.PageSize <= 0 {
		query.PageSize = 20
	}
	if query.PageSize > 100 {
		query.PageSize = 100
	}
	
	offset := (query.Page - 1) * query.PageSize
	alerts, total, err := s.alertRepo.List(ctx, query, query.PageSize, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to list alerts: %w", err)
	}
	
	totalPage := int(total) / query.PageSize
	if int(total)%query.PageSize > 0 {
		totalPage++
	}
	
	return &model.ListResponse{
		Data: alerts,
		Pagination: model.PaginationResponse{
			Page:      query.Page,
			PageSize:  query.PageSize,
			Total:     total,
			TotalPage: totalPage,
		},
	}, nil
}

// ProcessResourceChange 处理资源变更
func (s *alertService) ProcessResourceChange(ctx context.Context, resource *model.Resource, changeType model.ChangeType) error {
	// 检查资源状态变更
	if changeType == model.ChangeTypeUpdate {
		if err := s.checkResourceStatusChange(ctx, resource); err != nil {
			logger.Errorf("Failed to check resource status change: %v", err)
		}
	}
	
	// 检查资源删除
	if changeType == model.ChangeTypeDelete {
		if err := s.createResourceDeletedAlert(ctx, resource); err != nil {
			logger.Errorf("Failed to create resource deleted alert: %v", err)
		}
	}
	
	return nil
}

// CheckResourceHealth 检查资源健康状态
func (s *alertService) CheckResourceHealth(ctx context.Context, resourceID uuid.UUID) error {
	resource, err := s.resourceRepo.GetByID(ctx, resourceID)
	if err != nil {
		return fmt.Errorf("failed to get resource: %w", err)
	}
	if resource == nil {
		return fmt.Errorf("resource not found")
	}
	
	// 检查资源状态
	if resource.Status == model.ResourceStatusStopped {
		// 检查是否已有相同的告警
		existing, err := s.alertRepo.GetByResourceAndRule(ctx, resourceID, "resource_stopped")
		if err != nil {
			return err
		}
		
		if existing == nil || existing.Status == model.AlertStatusResolved {
			// 创建告警
			req := &model.CreateAlertRequest{
				ResourceID:  resourceID,
				Title:       fmt.Sprintf("Resource %s is stopped", resource.Name),
				Description: fmt.Sprintf("Resource %s (ID: %s) is in stopped state", resource.Name, resource.CloudID),
				Level:       model.AlertLevelWarning,
				Source:      "health_check",
				RuleID:      "resource_stopped",
				Tags: map[string]interface{}{
					"resource_type": resource.Type,
					"provider":      resource.Provider,
					"region":        resource.Region,
				},
			}
			
			if _, err := s.CreateAlert(ctx, req); err != nil {
				return fmt.Errorf("failed to create health check alert: %w", err)
			}
		}
	} else if resource.Status == model.ResourceStatusRunning {
		// 如果资源恢复运行，解决相关告警
		existing, err := s.alertRepo.GetByResourceAndRule(ctx, resourceID, "resource_stopped")
		if err != nil {
			return err
		}
		
		if existing != nil && existing.Status == model.AlertStatusOpen {
			if err := s.ResolveAlert(ctx, existing.ID); err != nil {
				logger.Errorf("Failed to resolve alert: %v", err)
			}
		}
	}
	
	return nil
}

// checkResourceStatusChange 检查资源状态变更
func (s *alertService) checkResourceStatusChange(ctx context.Context, resource *model.Resource) error {
	// 这里可以实现更复杂的状态变更检查逻辑
	// 比如检查状态从运行变为停止，或者从健康变为异常等
	
	if resource.Status == model.ResourceStatusStopped {
		req := &model.CreateAlertRequest{
			ResourceID:  resource.ID,
			Title:       fmt.Sprintf("Resource %s status changed to stopped", resource.Name),
			Description: fmt.Sprintf("Resource %s status changed to stopped", resource.Name),
			Level:       model.AlertLevelWarning,
			Source:      "status_change",
			RuleID:      "status_change_stopped",
			Tags: map[string]interface{}{
				"old_status": "running", // 这里应该从变更记录中获取
				"new_status": resource.Status,
			},
		}
		
		_, err := s.CreateAlert(ctx, req)
		return err
	}
	
	return nil
}

// createResourceDeletedAlert 创建资源删除告警
func (s *alertService) createResourceDeletedAlert(ctx context.Context, resource *model.Resource) error {
	req := &model.CreateAlertRequest{
		ResourceID:  resource.ID,
		Title:       fmt.Sprintf("Resource %s has been deleted", resource.Name),
		Description: fmt.Sprintf("Resource %s (ID: %s) has been deleted from cloud provider", resource.Name, resource.CloudID),
		Level:       model.AlertLevelCritical,
		Source:      "resource_deletion",
		RuleID:      "resource_deleted",
		Tags: map[string]interface{}{
			"resource_type": resource.Type,
			"provider":      resource.Provider,
			"region":        resource.Region,
		},
	}
	
	_, err := s.CreateAlert(ctx, req)
	return err
}
