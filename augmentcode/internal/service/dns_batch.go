package service

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"cmdb-platform/internal/model"
	"cmdb-platform/pkg/cloud"
	"cmdb-platform/pkg/logger"

	"github.com/google/uuid"
)

// BatchCreateRecords 批量创建DNS记录
func (s *dnsService) BatchCreateRecords(ctx context.Context, req *model.BatchCreateDNSRecordsRequest, userID uuid.UUID) ([]*model.DNSRecord, error) {
	// 获取域名信息
	zone, err := s.zoneRepo.GetByID(ctx, req.ZoneID)
	if err != nil {
		return nil, fmt.Errorf("failed to get DNS zone: %w", err)
	}
	if zone == nil {
		return nil, fmt.Errorf("DNS zone not found")
	}

	// 获取云服务提供商
	provider, err := s.cloudManager.GetProvider(zone.Provider)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud provider: %w", err)
	}

	dnsProvider := provider.DNSProvider()

	var createdRecords []*model.DNSRecord
	var cloudRecords []*cloud.DNSRecord

	// 准备云端记录
	for _, recordReq := range req.Records {
		cloudRecord := &cloud.DNSRecord{
			Name:        recordReq.Name,
			Type:        string(recordReq.Type),
			Value:       recordReq.Value,
			TTL:         recordReq.TTL,
			Priority:    recordReq.Priority,
			Weight:      recordReq.Weight,
			Port:        recordReq.Port,
			Description: recordReq.Description,
			Tags:        convertToStringMap(recordReq.Tags),
		}

		// 验证记录
		if err := dnsProvider.ValidateDNSRecord(ctx, cloudRecord); err != nil {
			return nil, fmt.Errorf("DNS record validation failed for %s: %w", recordReq.Name, err)
		}

		cloudRecords = append(cloudRecords, cloudRecord)
	}

	// 批量创建云端记录
	createdCloudRecords, err := dnsProvider.BatchCreateDNSRecords(ctx, zone.CloudZoneID, cloudRecords)
	if err != nil {
		return nil, fmt.Errorf("failed to batch create DNS records in cloud: %w", err)
	}

	// 创建本地记录
	var localRecords []*model.DNSRecord
	now := time.Now()

	for i, recordReq := range req.Records {
		if i >= len(createdCloudRecords) {
			break
		}

		record := &model.DNSRecord{
			TenantModel: model.TenantModel{
				BaseModel: model.BaseModel{
					ID:        uuid.New(),
					CreatedAt: now,
					UpdatedAt: now,
					CreatedBy: userID,
					UpdatedBy: userID,
				},
				TenantID: zone.TenantID,
			},
			ZoneID:        req.ZoneID,
			CloudRecordID: createdCloudRecords[i].ID,
			Name:          recordReq.Name,
			Type:          recordReq.Type,
			Value:         recordReq.Value,
			TTL:           recordReq.TTL,
			Priority:      recordReq.Priority,
			Weight:        recordReq.Weight,
			Port:          recordReq.Port,
			Status:        model.DNSRecordStatus(createdCloudRecords[i].Status),
			Description:   recordReq.Description,
			Tags:          convertToJSON(recordReq.Tags),
			LastSyncAt:    &time.Time{},
		}

		localRecords = append(localRecords, record)
		createdRecords = append(createdRecords, record)
	}

	// 批量创建本地记录
	if err := s.recordRepo.BatchCreate(ctx, localRecords); err != nil {
		// 如果本地创建失败，尝试删除云端记录
		var cloudRecordIDs []string
		for _, cloudRecord := range createdCloudRecords {
			cloudRecordIDs = append(cloudRecordIDs, cloudRecord.ID)
		}
		if deleteErr := dnsProvider.BatchDeleteDNSRecords(ctx, zone.CloudZoneID, cloudRecordIDs); deleteErr != nil {
			logger.Error("Failed to cleanup cloud DNS records after local batch creation failure", 
				"error", deleteErr, "count", len(cloudRecordIDs))
		}
		return nil, fmt.Errorf("failed to batch create local DNS records: %w", err)
	}

	// 记录操作日志
	for _, record := range createdRecords {
		s.logOperation(ctx, &zone.ID, &record.ID, "create", "record", record.Name, nil, record, "success", "", userID, 0)
	}

	return createdRecords, nil
}

// BatchUpdateRecords 批量更新DNS记录
func (s *dnsService) BatchUpdateRecords(ctx context.Context, req *model.BatchUpdateDNSRecordsRequest, userID uuid.UUID) ([]*model.DNSRecord, error) {
	var updatedRecords []*model.DNSRecord

	for _, recordUpdate := range req.Records {
		record, err := s.UpdateRecord(ctx, recordUpdate.ID, &recordUpdate.UpdateDNSRecordRequest, userID)
		if err != nil {
			logger.Error("Failed to update DNS record in batch", "error", err, "recordID", recordUpdate.ID)
			continue
		}
		updatedRecords = append(updatedRecords, record)
	}

	return updatedRecords, nil
}

// BatchDeleteRecords 批量删除DNS记录
func (s *dnsService) BatchDeleteRecords(ctx context.Context, req *model.BatchDeleteDNSRecordsRequest, userID uuid.UUID) error {
	var errors []error

	for _, recordID := range req.RecordIDs {
		if err := s.DeleteRecord(ctx, recordID, userID); err != nil {
			errors = append(errors, err)
			logger.Error("Failed to delete DNS record in batch", "error", err, "recordID", recordID)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("batch delete failed with %d errors: %v", len(errors), errors[0])
	}

	return nil
}

// ImportRecords 导入DNS记录
func (s *dnsService) ImportRecords(ctx context.Context, req *model.ImportDNSRecordsRequest, userID uuid.UUID) ([]*model.DNSRecord, error) {
	// 获取域名信息
	zone, err := s.zoneRepo.GetByID(ctx, req.ZoneID)
	if err != nil {
		return nil, fmt.Errorf("failed to get DNS zone: %w", err)
	}
	if zone == nil {
		return nil, fmt.Errorf("DNS zone not found")
	}

	var records []model.CreateDNSRecordRequest

	switch req.Format {
	case "json":
		if err := json.Unmarshal([]byte(req.Data), &records); err != nil {
			return nil, fmt.Errorf("failed to parse JSON data: %w", err)
		}
	case "csv":
		records, err = s.parseCSVRecords(req.Data)
		if err != nil {
			return nil, fmt.Errorf("failed to parse CSV data: %w", err)
		}
	case "bind":
		records, err = s.parseBindRecords(req.Data)
		if err != nil {
			return nil, fmt.Errorf("failed to parse BIND data: %w", err)
		}
	default:
		return nil, fmt.Errorf("unsupported format: %s", req.Format)
	}

	// 批量创建记录
	batchReq := &model.BatchCreateDNSRecordsRequest{
		ZoneID:  req.ZoneID,
		Records: records,
	}

	return s.BatchCreateRecords(ctx, batchReq, userID)
}

// ExportRecords 导出DNS记录
func (s *dnsService) ExportRecords(ctx context.Context, req *model.ExportDNSRecordsRequest) (string, error) {
	// 获取记录列表
	records, err := s.recordRepo.ListByZone(ctx, req.ZoneID)
	if err != nil {
		return "", fmt.Errorf("failed to list DNS records: %w", err)
	}

	switch req.Format {
	case "json":
		data, err := json.MarshalIndent(records, "", "  ")
		if err != nil {
			return "", fmt.Errorf("failed to marshal JSON: %w", err)
		}
		return string(data), nil
	case "csv":
		return s.exportCSVRecords(records)
	case "bind":
		return s.exportBindRecords(records)
	default:
		return "", fmt.Errorf("unsupported format: %s", req.Format)
	}
}

// ListOperationLogs 列出DNS操作日志
func (s *dnsService) ListOperationLogs(ctx context.Context, query *model.DNSOperationLogQuery) ([]*model.DNSOperationLog, int64, error) {
	return s.logRepo.List(ctx, query)
}

// parseCSVRecords 解析CSV格式的DNS记录
func (s *dnsService) parseCSVRecords(data string) ([]model.CreateDNSRecordRequest, error) {
	reader := csv.NewReader(strings.NewReader(data))
	lines, err := reader.ReadAll()
	if err != nil {
		return nil, err
	}

	var records []model.CreateDNSRecordRequest
	for i, line := range lines {
		if i == 0 {
			// 跳过标题行
			continue
		}
		if len(line) < 4 {
			continue
		}

		ttl, _ := strconv.Atoi(line[3])
		var priority *int
		if len(line) > 4 && line[4] != "" {
			if p, err := strconv.Atoi(line[4]); err == nil {
				priority = &p
			}
		}

		record := model.CreateDNSRecordRequest{
			Name:     line[0],
			Type:     model.DNSRecordType(line[1]),
			Value:    line[2],
			TTL:      ttl,
			Priority: priority,
		}
		records = append(records, record)
	}

	return records, nil
}

// parseBindRecords 解析BIND格式的DNS记录
func (s *dnsService) parseBindRecords(data string) ([]model.CreateDNSRecordRequest, error) {
	var records []model.CreateDNSRecordRequest
	lines := strings.Split(data, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, ";") {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) < 4 {
			continue
		}

		ttl, _ := strconv.Atoi(parts[1])
		record := model.CreateDNSRecordRequest{
			Name:  parts[0],
			TTL:   ttl,
			Type:  model.DNSRecordType(parts[3]),
			Value: strings.Join(parts[4:], " "),
		}
		records = append(records, record)
	}

	return records, nil
}

// exportCSVRecords 导出CSV格式的DNS记录
func (s *dnsService) exportCSVRecords(records []*model.DNSRecord) (string, error) {
	var builder strings.Builder
	writer := csv.NewWriter(&builder)

	// 写入标题行
	writer.Write([]string{"Name", "Type", "Value", "TTL", "Priority", "Description"})

	// 写入数据行
	for _, record := range records {
		priority := ""
		if record.Priority != nil {
			priority = strconv.Itoa(*record.Priority)
		}

		writer.Write([]string{
			record.Name,
			string(record.Type),
			record.Value,
			strconv.Itoa(record.TTL),
			priority,
			record.Description,
		})
	}

	writer.Flush()
	return builder.String(), nil
}

// exportBindRecords 导出BIND格式的DNS记录
func (s *dnsService) exportBindRecords(records []*model.DNSRecord) (string, error) {
	var builder strings.Builder

	for _, record := range records {
		builder.WriteString(fmt.Sprintf("%s\t%d\tIN\t%s\t%s\n",
			record.Name,
			record.TTL,
			record.Type,
			record.Value,
		))
	}

	return builder.String(), nil
}
