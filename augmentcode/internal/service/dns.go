package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"cmdb-platform/internal/model"
	"cmdb-platform/internal/repository"
	"cmdb-platform/pkg/cloud"
	"cmdb-platform/pkg/logger"

	"github.com/google/uuid"
	"gorm.io/datatypes"
)

// DNSService DNS管理服务接口
type DNSService interface {
	// DNS域名管理
	CreateZone(ctx context.Context, req *model.CreateDNSZoneRequest, userID uuid.UUID) (*model.DNSZone, error)
	GetZone(ctx context.Context, id uuid.UUID) (*model.DNSZone, error)
	UpdateZone(ctx context.Context, id uuid.UUID, req *model.UpdateDNSZoneRequest, userID uuid.UUID) (*model.DNSZone, error)
	DeleteZone(ctx context.Context, id uuid.UUID, userID uuid.UUID) error
	ListZones(ctx context.Context, query *model.DNSZoneQuery) ([]*model.DNSZone, int64, error)
	SyncZones(ctx context.Context, cloudAccountID uuid.UUID, userID uuid.UUID) error

	// DNS记录管理
	CreateRecord(ctx context.Context, req *model.CreateDNSRecordRequest, userID uuid.UUID) (*model.DNSRecord, error)
	GetRecord(ctx context.Context, id uuid.UUID) (*model.DNSRecord, error)
	UpdateRecord(ctx context.Context, id uuid.UUID, req *model.UpdateDNSRecordRequest, userID uuid.UUID) (*model.DNSRecord, error)
	DeleteRecord(ctx context.Context, id uuid.UUID, userID uuid.UUID) error
	ListRecords(ctx context.Context, query *model.DNSRecordQuery) ([]*model.DNSRecord, int64, error)
	SyncRecords(ctx context.Context, zoneID uuid.UUID, userID uuid.UUID) error

	// 批量操作
	BatchCreateRecords(ctx context.Context, req *model.BatchCreateDNSRecordsRequest, userID uuid.UUID) ([]*model.DNSRecord, error)
	BatchUpdateRecords(ctx context.Context, req *model.BatchUpdateDNSRecordsRequest, userID uuid.UUID) ([]*model.DNSRecord, error)
	BatchDeleteRecords(ctx context.Context, req *model.BatchDeleteDNSRecordsRequest, userID uuid.UUID) error

	// 导入导出
	ImportRecords(ctx context.Context, req *model.ImportDNSRecordsRequest, userID uuid.UUID) ([]*model.DNSRecord, error)
	ExportRecords(ctx context.Context, req *model.ExportDNSRecordsRequest) (string, error)

	// 操作日志
	ListOperationLogs(ctx context.Context, query *model.DNSOperationLogQuery) ([]*model.DNSOperationLog, int64, error)
}

// dnsService DNS管理服务实现
type dnsService struct {
	zoneRepo     repository.DNSZoneRepository
	recordRepo   repository.DNSRecordRepository
	logRepo      repository.DNSOperationLogRepository
	accountRepo  repository.CloudAccountRepository
	cloudManager *cloud.Manager
}

// NewDNSService 创建DNS管理服务
func NewDNSService(
	zoneRepo repository.DNSZoneRepository,
	recordRepo repository.DNSRecordRepository,
	logRepo repository.DNSOperationLogRepository,
	accountRepo repository.CloudAccountRepository,
	cloudManager *cloud.Manager,
) DNSService {
	return &dnsService{
		zoneRepo:     zoneRepo,
		recordRepo:   recordRepo,
		logRepo:      logRepo,
		accountRepo:  accountRepo,
		cloudManager: cloudManager,
	}
}

// CreateZone 创建DNS域名
func (s *dnsService) CreateZone(ctx context.Context, req *model.CreateDNSZoneRequest, userID uuid.UUID) (*model.DNSZone, error) {
	// 获取云账户信息
	account, err := s.accountRepo.GetByID(ctx, req.CloudAccountID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud account: %w", err)
	}
	if account == nil {
		return nil, fmt.Errorf("cloud account not found")
	}

	// 检查域名是否已存在
	existingZone, err := s.zoneRepo.GetByDomainName(ctx, account.TenantID, req.DomainName)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing zone: %w", err)
	}
	if existingZone != nil {
		return nil, fmt.Errorf("domain name already exists: %s", req.DomainName)
	}

	// 获取云服务提供商
	provider, err := s.cloudManager.GetProvider(account.Provider)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud provider: %w", err)
	}

	dnsProvider := provider.DNSProvider()

	// 在云服务商创建域名
	cloudZone := &cloud.DNSZone{
		DomainName:  req.DomainName,
		TTL:         req.TTL,
		Description: req.Description,
		Tags:        convertToStringMap(req.Tags),
	}

	createdCloudZone, err := dnsProvider.CreateDNSZone(ctx, cloudZone)
	if err != nil {
		return nil, fmt.Errorf("failed to create DNS zone in cloud: %w", err)
	}

	// 创建本地记录
	zone := &model.DNSZone{
		TenantModel: model.TenantModel{
			BaseModel: model.BaseModel{
				ID:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
				CreatedBy: userID,
				UpdatedBy: userID,
			},
			TenantID: account.TenantID,
		},
		CloudAccountID: req.CloudAccountID,
		Provider:       account.Provider,
		CloudZoneID:    createdCloudZone.ID,
		DomainName:     req.DomainName,
		Status:         model.DNSZoneStatus(createdCloudZone.Status),
		TTL:            req.TTL,
		Description:    req.Description,
		Tags:           convertToJSON(req.Tags),
		LastSyncAt:     &time.Time{},
	}

	if err := s.zoneRepo.Create(ctx, zone); err != nil {
		// 如果本地创建失败，尝试删除云端域名
		if deleteErr := dnsProvider.DeleteDNSZone(ctx, createdCloudZone.ID); deleteErr != nil {
			logger.Error("Failed to cleanup cloud DNS zone after local creation failure",
				"error", deleteErr, "cloudZoneID", createdCloudZone.ID)
		}
		return nil, fmt.Errorf("failed to create local DNS zone: %w", err)
	}

	// 记录操作日志
	s.logOperation(ctx, &zone.ID, nil, "create", "zone", req.DomainName, nil, zone, "success", "", userID, 0)

	return zone, nil
}

// GetZone 获取DNS域名
func (s *dnsService) GetZone(ctx context.Context, id uuid.UUID) (*model.DNSZone, error) {
	zone, err := s.zoneRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get DNS zone: %w", err)
	}
	if zone == nil {
		return nil, fmt.Errorf("DNS zone not found")
	}
	return zone, nil
}

// UpdateZone 更新DNS域名
func (s *dnsService) UpdateZone(ctx context.Context, id uuid.UUID, req *model.UpdateDNSZoneRequest, userID uuid.UUID) (*model.DNSZone, error) {
	// 获取现有域名
	zone, err := s.zoneRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get DNS zone: %w", err)
	}
	if zone == nil {
		return nil, fmt.Errorf("DNS zone not found")
	}

	// 获取云服务提供商
	provider, err := s.cloudManager.GetProvider(zone.Provider)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud provider: %w", err)
	}

	dnsProvider := provider.DNSProvider()

	// 记录原始值
	oldZone := *zone

	// 更新云端域名
	cloudZone := &cloud.DNSZone{
		DomainName:  zone.DomainName,
		TTL:         req.TTL,
		Description: req.Description,
		Tags:        convertToStringMap(req.Tags),
	}

	_, err = dnsProvider.UpdateDNSZone(ctx, zone.CloudZoneID, cloudZone)
	if err != nil {
		return nil, fmt.Errorf("failed to update DNS zone in cloud: %w", err)
	}

	// 更新本地记录
	if req.TTL > 0 {
		zone.TTL = req.TTL
	}
	if req.Description != "" {
		zone.Description = req.Description
	}
	if req.Status != "" {
		zone.Status = req.Status
	}
	if req.Tags != nil {
		zone.Tags = convertToJSON(req.Tags)
	}
	zone.UpdatedAt = time.Now()
	zone.UpdatedBy = userID

	if err := s.zoneRepo.Update(ctx, zone); err != nil {
		return nil, fmt.Errorf("failed to update local DNS zone: %w", err)
	}

	// 记录操作日志
	s.logOperation(ctx, &zone.ID, nil, "update", "zone", zone.DomainName, &oldZone, zone, "success", "", userID, 0)

	return zone, nil
}

// DeleteZone 删除DNS域名
func (s *dnsService) DeleteZone(ctx context.Context, id uuid.UUID, userID uuid.UUID) error {
	// 获取域名信息
	zone, err := s.zoneRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get DNS zone: %w", err)
	}
	if zone == nil {
		return fmt.Errorf("DNS zone not found")
	}

	// 检查是否还有DNS记录
	records, err := s.recordRepo.ListByZone(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to check DNS records: %w", err)
	}
	if len(records) > 0 {
		return fmt.Errorf("cannot delete zone with existing DNS records")
	}

	// 获取云服务提供商
	provider, err := s.cloudManager.GetProvider(zone.Provider)
	if err != nil {
		return fmt.Errorf("failed to get cloud provider: %w", err)
	}

	dnsProvider := provider.DNSProvider()

	// 删除云端域名
	if err := dnsProvider.DeleteDNSZone(ctx, zone.CloudZoneID); err != nil {
		return fmt.Errorf("failed to delete DNS zone in cloud: %w", err)
	}

	// 删除本地记录
	if err := s.zoneRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete local DNS zone: %w", err)
	}

	// 记录操作日志
	s.logOperation(ctx, &zone.ID, nil, "delete", "zone", zone.DomainName, zone, nil, "success", "", userID, 0)

	return nil
}

// ListZones 列出DNS域名
func (s *dnsService) ListZones(ctx context.Context, query *model.DNSZoneQuery) ([]*model.DNSZone, int64, error) {
	return s.zoneRepo.List(ctx, query)
}

// 辅助函数
func convertToStringMap(data map[string]interface{}) map[string]string {
	result := make(map[string]string)
	for k, v := range data {
		if str, ok := v.(string); ok {
			result[k] = str
		} else {
			result[k] = fmt.Sprintf("%v", v)
		}
	}
	return result
}

func convertToJSON(data map[string]interface{}) datatypes.JSON {
	if data == nil {
		data = make(map[string]interface{})
	}
	jsonData, _ := json.Marshal(data)
	return datatypes.JSON(jsonData)
}

// logOperation 记录操作日志
func (s *dnsService) logOperation(ctx context.Context, zoneID *uuid.UUID, recordID *uuid.UUID,
	operation, target, targetName string, oldValue, newValue interface{},
	status, errorMsg string, userID uuid.UUID, duration int) {

	var oldValueJSON, newValueJSON datatypes.JSON

	if oldValue != nil {
		if data, err := json.Marshal(oldValue); err == nil {
			oldValueJSON = datatypes.JSON(data)
		}
	}

	if newValue != nil {
		if data, err := json.Marshal(newValue); err == nil {
			newValueJSON = datatypes.JSON(data)
		}
	}

	log := &model.DNSOperationLog{
		TenantModel: model.TenantModel{
			BaseModel: model.BaseModel{
				ID:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
				CreatedBy: userID,
				UpdatedBy: userID,
			},
			// TenantID will be set based on zone or record
		},
		ZoneID:     zoneID,
		RecordID:   recordID,
		Operation:  operation,
		Target:     target,
		TargetName: targetName,
		OldValue:   oldValueJSON,
		NewValue:   newValueJSON,
		Status:     status,
		ErrorMsg:   errorMsg,
		Duration:   duration,
	}

	// 设置租户ID
	if zoneID != nil {
		if zone, err := s.zoneRepo.GetByID(ctx, *zoneID); err == nil && zone != nil {
			log.TenantID = zone.TenantID
		}
	} else if recordID != nil {
		if record, err := s.recordRepo.GetByID(ctx, *recordID); err == nil && record != nil {
			if zone, err := s.zoneRepo.GetByID(ctx, record.ZoneID); err == nil && zone != nil {
				log.TenantID = zone.TenantID
			}
		}
	}

	if err := s.logRepo.Create(ctx, log); err != nil {
		logger.Error("Failed to create DNS operation log", "error", err)
	}
}

// SyncZones 同步DNS域名
func (s *dnsService) SyncZones(ctx context.Context, cloudAccountID uuid.UUID, userID uuid.UUID) error {
	// 获取云账户信息
	account, err := s.accountRepo.GetByID(ctx, cloudAccountID)
	if err != nil {
		return fmt.Errorf("failed to get cloud account: %w", err)
	}
	if account == nil {
		return fmt.Errorf("cloud account not found")
	}

	// 获取云服务提供商
	provider, err := s.cloudManager.GetProvider(account.Provider)
	if err != nil {
		return fmt.Errorf("failed to get cloud provider: %w", err)
	}

	dnsProvider := provider.DNSProvider()

	// 获取云端域名列表
	cloudZones, err := dnsProvider.ListDNSZones(ctx)
	if err != nil {
		return fmt.Errorf("failed to list cloud DNS zones: %w", err)
	}

	// 获取本地域名列表
	localZones, err := s.zoneRepo.ListByCloudAccount(ctx, cloudAccountID)
	if err != nil {
		return fmt.Errorf("failed to list local DNS zones: %w", err)
	}

	// 创建本地域名映射
	localZoneMap := make(map[string]*model.DNSZone)
	for _, zone := range localZones {
		localZoneMap[zone.CloudZoneID] = zone
	}

	now := time.Now()

	// 同步云端域名到本地
	for _, cloudZone := range cloudZones {
		if localZone, exists := localZoneMap[cloudZone.ID]; exists {
			// 更新现有域名
			localZone.Status = model.DNSZoneStatus(cloudZone.Status)
			localZone.Description = cloudZone.Description
			localZone.LastSyncAt = &now
			localZone.UpdatedAt = now
			localZone.UpdatedBy = userID

			if err := s.zoneRepo.Update(ctx, localZone); err != nil {
				logger.Error("Failed to update DNS zone during sync", "error", err, "zoneID", localZone.ID)
			}
		} else {
			// 创建新域名
			zone := &model.DNSZone{
				TenantModel: model.TenantModel{
					BaseModel: model.BaseModel{
						ID:        uuid.New(),
						CreatedAt: now,
						UpdatedAt: now,
						CreatedBy: userID,
						UpdatedBy: userID,
					},
					TenantID: account.TenantID,
				},
				CloudAccountID: cloudAccountID,
				Provider:       account.Provider,
				CloudZoneID:    cloudZone.ID,
				DomainName:     cloudZone.DomainName,
				Status:         model.DNSZoneStatus(cloudZone.Status),
				TTL:            cloudZone.TTL,
				Description:    cloudZone.Description,
				Tags:           convertToJSON(make(map[string]interface{})),
				LastSyncAt:     &now,
			}

			if err := s.zoneRepo.Create(ctx, zone); err != nil {
				logger.Error("Failed to create DNS zone during sync", "error", err, "domain", cloudZone.DomainName)
			}
		}
	}

	return nil
}

// CreateRecord 创建DNS记录
func (s *dnsService) CreateRecord(ctx context.Context, req *model.CreateDNSRecordRequest, userID uuid.UUID) (*model.DNSRecord, error) {
	// 获取域名信息
	zone, err := s.zoneRepo.GetByID(ctx, req.ZoneID)
	if err != nil {
		return nil, fmt.Errorf("failed to get DNS zone: %w", err)
	}
	if zone == nil {
		return nil, fmt.Errorf("DNS zone not found")
	}

	// 获取云服务提供商
	provider, err := s.cloudManager.GetProvider(zone.Provider)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud provider: %w", err)
	}

	dnsProvider := provider.DNSProvider()

	// 验证DNS记录
	cloudRecord := &cloud.DNSRecord{
		Name:        req.Name,
		Type:        string(req.Type),
		Value:       req.Value,
		TTL:         req.TTL,
		Priority:    req.Priority,
		Weight:      req.Weight,
		Port:        req.Port,
		Description: req.Description,
		Tags:        convertToStringMap(req.Tags),
	}

	if err := dnsProvider.ValidateDNSRecord(ctx, cloudRecord); err != nil {
		return nil, fmt.Errorf("DNS record validation failed: %w", err)
	}

	// 在云服务商创建记录
	createdCloudRecord, err := dnsProvider.CreateDNSRecord(ctx, zone.CloudZoneID, cloudRecord)
	if err != nil {
		return nil, fmt.Errorf("failed to create DNS record in cloud: %w", err)
	}

	// 创建本地记录
	record := &model.DNSRecord{
		TenantModel: model.TenantModel{
			BaseModel: model.BaseModel{
				ID:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
				CreatedBy: userID,
				UpdatedBy: userID,
			},
			TenantID: zone.TenantID,
		},
		ZoneID:        req.ZoneID,
		CloudRecordID: createdCloudRecord.ID,
		Name:          req.Name,
		Type:          req.Type,
		Value:         req.Value,
		TTL:           req.TTL,
		Priority:      req.Priority,
		Weight:        req.Weight,
		Port:          req.Port,
		Status:        model.DNSRecordStatus(createdCloudRecord.Status),
		Description:   req.Description,
		Tags:          convertToJSON(req.Tags),
		LastSyncAt:    &time.Time{},
	}

	if err := s.recordRepo.Create(ctx, record); err != nil {
		// 如果本地创建失败，尝试删除云端记录
		if deleteErr := dnsProvider.DeleteDNSRecord(ctx, zone.CloudZoneID, createdCloudRecord.ID); deleteErr != nil {
			logger.Error("Failed to cleanup cloud DNS record after local creation failure",
				"error", deleteErr, "cloudRecordID", createdCloudRecord.ID)
		}
		return nil, fmt.Errorf("failed to create local DNS record: %w", err)
	}

	// 记录操作日志
	s.logOperation(ctx, &zone.ID, &record.ID, "create", "record", req.Name, nil, record, "success", "", userID, 0)

	return record, nil
}

// GetRecord 获取DNS记录
func (s *dnsService) GetRecord(ctx context.Context, id uuid.UUID) (*model.DNSRecord, error) {
	record, err := s.recordRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get DNS record: %w", err)
	}
	if record == nil {
		return nil, fmt.Errorf("DNS record not found")
	}
	return record, nil
}

// UpdateRecord 更新DNS记录
func (s *dnsService) UpdateRecord(ctx context.Context, id uuid.UUID, req *model.UpdateDNSRecordRequest, userID uuid.UUID) (*model.DNSRecord, error) {
	// 获取现有记录
	record, err := s.recordRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get DNS record: %w", err)
	}
	if record == nil {
		return nil, fmt.Errorf("DNS record not found")
	}

	// 获取域名信息
	zone, err := s.zoneRepo.GetByID(ctx, record.ZoneID)
	if err != nil {
		return nil, fmt.Errorf("failed to get DNS zone: %w", err)
	}

	// 获取云服务提供商
	provider, err := s.cloudManager.GetProvider(zone.Provider)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud provider: %w", err)
	}

	dnsProvider := provider.DNSProvider()

	// 记录原始值
	oldRecord := *record

	// 更新云端记录
	cloudRecord := &cloud.DNSRecord{
		Name:        record.Name, // 保持原有名称
		Type:        string(record.Type),
		Value:       record.Value,
		TTL:         record.TTL,
		Priority:    record.Priority,
		Weight:      record.Weight,
		Port:        record.Port,
		Description: record.Description,
		Tags:        convertToStringMap(req.Tags),
	}

	if req.Name != "" {
		cloudRecord.Name = req.Name
	}
	if req.Value != "" {
		cloudRecord.Value = req.Value
	}
	if req.TTL > 0 {
		cloudRecord.TTL = req.TTL
	}
	if req.Priority != nil {
		cloudRecord.Priority = req.Priority
	}
	if req.Weight != nil {
		cloudRecord.Weight = req.Weight
	}
	if req.Port != nil {
		cloudRecord.Port = req.Port
	}
	if req.Description != "" {
		cloudRecord.Description = req.Description
	}

	_, err = dnsProvider.UpdateDNSRecord(ctx, zone.CloudZoneID, record.CloudRecordID, cloudRecord)
	if err != nil {
		return nil, fmt.Errorf("failed to update DNS record in cloud: %w", err)
	}

	// 更新本地记录
	if req.Name != "" {
		record.Name = req.Name
	}
	if req.Value != "" {
		record.Value = req.Value
	}
	if req.TTL > 0 {
		record.TTL = req.TTL
	}
	if req.Priority != nil {
		record.Priority = req.Priority
	}
	if req.Weight != nil {
		record.Weight = req.Weight
	}
	if req.Port != nil {
		record.Port = req.Port
	}
	if req.Status != "" {
		record.Status = req.Status
	}
	if req.Description != "" {
		record.Description = req.Description
	}
	if req.Tags != nil {
		record.Tags = convertToJSON(req.Tags)
	}
	record.UpdatedAt = time.Now()
	record.UpdatedBy = userID

	if err := s.recordRepo.Update(ctx, record); err != nil {
		return nil, fmt.Errorf("failed to update local DNS record: %w", err)
	}

	// 记录操作日志
	s.logOperation(ctx, &zone.ID, &record.ID, "update", "record", record.Name, &oldRecord, record, "success", "", userID, 0)

	return record, nil
}

// DeleteRecord 删除DNS记录
func (s *dnsService) DeleteRecord(ctx context.Context, id uuid.UUID, userID uuid.UUID) error {
	// 获取记录信息
	record, err := s.recordRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get DNS record: %w", err)
	}
	if record == nil {
		return fmt.Errorf("DNS record not found")
	}

	// 获取域名信息
	zone, err := s.zoneRepo.GetByID(ctx, record.ZoneID)
	if err != nil {
		return fmt.Errorf("failed to get DNS zone: %w", err)
	}

	// 获取云服务提供商
	provider, err := s.cloudManager.GetProvider(zone.Provider)
	if err != nil {
		return fmt.Errorf("failed to get cloud provider: %w", err)
	}

	dnsProvider := provider.DNSProvider()

	// 删除云端记录
	if err := dnsProvider.DeleteDNSRecord(ctx, zone.CloudZoneID, record.CloudRecordID); err != nil {
		return fmt.Errorf("failed to delete DNS record in cloud: %w", err)
	}

	// 删除本地记录
	if err := s.recordRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete local DNS record: %w", err)
	}

	// 记录操作日志
	s.logOperation(ctx, &zone.ID, &record.ID, "delete", "record", record.Name, record, nil, "success", "", userID, 0)

	return nil
}

// ListRecords 列出DNS记录
func (s *dnsService) ListRecords(ctx context.Context, query *model.DNSRecordQuery) ([]*model.DNSRecord, int64, error) {
	return s.recordRepo.List(ctx, query)
}

// SyncRecords 同步DNS记录
func (s *dnsService) SyncRecords(ctx context.Context, zoneID uuid.UUID, userID uuid.UUID) error {
	// 获取域名信息
	zone, err := s.zoneRepo.GetByID(ctx, zoneID)
	if err != nil {
		return fmt.Errorf("failed to get DNS zone: %w", err)
	}
	if zone == nil {
		return fmt.Errorf("DNS zone not found")
	}

	// 获取云服务提供商
	provider, err := s.cloudManager.GetProvider(zone.Provider)
	if err != nil {
		return fmt.Errorf("failed to get cloud provider: %w", err)
	}

	dnsProvider := provider.DNSProvider()

	// 获取云端记录列表
	cloudRecords, err := dnsProvider.ListDNSRecords(ctx, zone.CloudZoneID)
	if err != nil {
		return fmt.Errorf("failed to list cloud DNS records: %w", err)
	}

	// 获取本地记录列表
	localRecords, err := s.recordRepo.ListByZone(ctx, zoneID)
	if err != nil {
		return fmt.Errorf("failed to list local DNS records: %w", err)
	}

	// 创建本地记录映射
	localRecordMap := make(map[string]*model.DNSRecord)
	for _, record := range localRecords {
		localRecordMap[record.CloudRecordID] = record
	}

	now := time.Now()

	// 同步云端记录到本地
	for _, cloudRecord := range cloudRecords {
		if localRecord, exists := localRecordMap[cloudRecord.ID]; exists {
			// 更新现有记录
			localRecord.Name = cloudRecord.Name
			localRecord.Type = model.DNSRecordType(cloudRecord.Type)
			localRecord.Value = cloudRecord.Value
			localRecord.TTL = cloudRecord.TTL
			localRecord.Priority = cloudRecord.Priority
			localRecord.Weight = cloudRecord.Weight
			localRecord.Port = cloudRecord.Port
			localRecord.Status = model.DNSRecordStatus(cloudRecord.Status)
			localRecord.Description = cloudRecord.Description
			localRecord.LastSyncAt = &now
			localRecord.UpdatedAt = now
			localRecord.UpdatedBy = userID

			if err := s.recordRepo.Update(ctx, localRecord); err != nil {
				logger.Error("Failed to update DNS record during sync", "error", err, "recordID", localRecord.ID)
			}
		} else {
			// 创建新记录
			record := &model.DNSRecord{
				TenantModel: model.TenantModel{
					BaseModel: model.BaseModel{
						ID:        uuid.New(),
						CreatedAt: now,
						UpdatedAt: now,
						CreatedBy: userID,
						UpdatedBy: userID,
					},
					TenantID: zone.TenantID,
				},
				ZoneID:        zoneID,
				CloudRecordID: cloudRecord.ID,
				Name:          cloudRecord.Name,
				Type:          model.DNSRecordType(cloudRecord.Type),
				Value:         cloudRecord.Value,
				TTL:           cloudRecord.TTL,
				Priority:      cloudRecord.Priority,
				Weight:        cloudRecord.Weight,
				Port:          cloudRecord.Port,
				Status:        model.DNSRecordStatus(cloudRecord.Status),
				Description:   cloudRecord.Description,
				Tags:          convertToJSON(make(map[string]interface{})),
				LastSyncAt:    &now,
			}

			if err := s.recordRepo.Create(ctx, record); err != nil {
				logger.Error("Failed to create DNS record during sync", "error", err, "name", cloudRecord.Name)
			}
		}
	}

	return nil
}
