package routes

import (
	"cmdb-platform/internal/api"
	"cmdb-platform/internal/middleware"

	"github.com/gin-gonic/gin"
)

// SetupDNSRoutes 设置DNS相关路由
func SetupDNSRoutes(router *gin.RouterGroup, dnsHandler *api.DNSHandler) {
	// DNS域名管理路由
	zones := router.Group("/dns/zones")
	zones.Use(middleware.AuthMiddleware())
	{
		zones.POST("", dnsHandler.CreateZone)           // 创建DNS域名
		zones.GET("", dnsHandler.ListZones)             // 列出DNS域名
		zones.GET("/:id", dnsHandler.GetZone)           // 获取DNS域名详情
		zones.PUT("/:id", dnsHandler.UpdateZone)        // 更新DNS域名
		zones.DELETE("/:id", dnsHandler.DeleteZone)     // 删除DNS域名
		zones.POST("/sync", dnsHandler.SyncZones)       // 同步DNS域名
	}

	// DNS记录管理路由
	records := router.Group("/dns/records")
	records.Use(middleware.AuthMiddleware())
	{
		records.POST("", dnsHandler.CreateRecord)           // 创建DNS记录
		records.GET("", dnsHandler.ListRecords)             // 列出DNS记录
		records.GET("/:id", dnsHandler.GetRecord)           // 获取DNS记录详情
		records.PUT("/:id", dnsHandler.UpdateRecord)        // 更新DNS记录
		records.DELETE("/:id", dnsHandler.DeleteRecord)     // 删除DNS记录
		records.POST("/sync", dnsHandler.SyncRecords)       // 同步DNS记录
		
		// 批量操作
		records.POST("/batch", dnsHandler.BatchCreateRecords)   // 批量创建DNS记录
		records.PUT("/batch", dnsHandler.BatchUpdateRecords)    // 批量更新DNS记录
		records.DELETE("/batch", dnsHandler.BatchDeleteRecords) // 批量删除DNS记录
		
		// 导入导出
		records.POST("/import", dnsHandler.ImportRecords)       // 导入DNS记录
		records.GET("/export", dnsHandler.ExportRecords)        // 导出DNS记录
	}

	// DNS操作日志路由
	logs := router.Group("/dns/logs")
	logs.Use(middleware.AuthMiddleware())
	{
		logs.GET("", dnsHandler.ListOperationLogs)      // 列出DNS操作日志
	}
}
