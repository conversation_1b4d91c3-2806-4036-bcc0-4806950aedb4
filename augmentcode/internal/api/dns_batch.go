package api

import (
	"net/http"

	"cmdb-platform/internal/model"
	"cmdb-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ListRecords 列出DNS记录
// @Summary 列出DNS记录
// @Description 分页列出DNS记录
// @Tags DNS
// @Produce json
// @Param zone_id query string false "DNS域名ID"
// @Param name query string false "记录名称"
// @Param type query string false "记录类型"
// @Param value query string false "记录值"
// @Param status query string false "状态"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_order query string false "排序方向" default(desc)
// @Success 200 {object} model.APIResponse{data=model.ListResponse}
// @Failure 400 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/records [get]
func (h *DNSHandler) ListRecords(c *gin.Context) {
	var query model.DNSRecordQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		logger.Error("Failed to bind list DNS records query", "error", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid query parameters",
			Details: err.Error(),
		})
		return
	}

	// 设置默认值
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.PageSize <= 0 {
		query.PageSize = 20
	}
	if query.SortBy == "" {
		query.SortBy = "created_at"
	}
	if query.SortOrder == "" {
		query.SortOrder = "desc"
	}

	records, total, err := h.dnsService.ListRecords(c.Request.Context(), &query)
	if err != nil {
		logger.Error("Failed to list DNS records", "error", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to list DNS records",
			Details: err.Error(),
		})
		return
	}

	totalPage := (int(total) + query.PageSize - 1) / query.PageSize
	response := model.ListResponse{
		Data: records,
		Pagination: model.PaginationResponse{
			Page:      query.Page,
			PageSize:  query.PageSize,
			Total:     total,
			TotalPage: totalPage,
		},
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "Success",
		Data:    response,
	})
}

// SyncRecords 同步DNS记录
// @Summary 同步DNS记录
// @Description 从云服务商同步DNS记录到本地
// @Tags DNS
// @Produce json
// @Param zone_id query string true "DNS域名ID"
// @Success 200 {object} model.APIResponse
// @Failure 400 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/records/sync [post]
func (h *DNSHandler) SyncRecords(c *gin.Context) {
	zoneIDStr := c.Query("zone_id")
	if zoneIDStr == "" {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "zone_id is required",
		})
		return
	}

	zoneID, err := uuid.Parse(zoneIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid zone ID",
			Details: err.Error(),
		})
		return
	}

	userID := getUserID(c)
	if err := h.dnsService.SyncRecords(c.Request.Context(), zoneID, userID); err != nil {
		logger.Error("Failed to sync DNS records", "error", err, "zoneID", zoneID)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to sync DNS records",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "DNS records synced successfully",
	})
}

// BatchCreateRecords 批量创建DNS记录
// @Summary 批量创建DNS记录
// @Description 批量创建DNS记录
// @Tags DNS
// @Accept json
// @Produce json
// @Param request body model.BatchCreateDNSRecordsRequest true "批量创建DNS记录请求"
// @Success 200 {object} model.APIResponse{data=[]model.DNSRecord}
// @Failure 400 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/records/batch [post]
func (h *DNSHandler) BatchCreateRecords(c *gin.Context) {
	var req model.BatchCreateDNSRecordsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind batch create DNS records request", "error", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}

	userID := getUserID(c)
	records, err := h.dnsService.BatchCreateRecords(c.Request.Context(), &req, userID)
	if err != nil {
		logger.Error("Failed to batch create DNS records", "error", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to batch create DNS records",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "DNS records created successfully",
		Data:    records,
	})
}

// BatchUpdateRecords 批量更新DNS记录
// @Summary 批量更新DNS记录
// @Description 批量更新DNS记录
// @Tags DNS
// @Accept json
// @Produce json
// @Param request body model.BatchUpdateDNSRecordsRequest true "批量更新DNS记录请求"
// @Success 200 {object} model.APIResponse{data=[]model.DNSRecord}
// @Failure 400 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/records/batch [put]
func (h *DNSHandler) BatchUpdateRecords(c *gin.Context) {
	var req model.BatchUpdateDNSRecordsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind batch update DNS records request", "error", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}

	userID := getUserID(c)
	records, err := h.dnsService.BatchUpdateRecords(c.Request.Context(), &req, userID)
	if err != nil {
		logger.Error("Failed to batch update DNS records", "error", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to batch update DNS records",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "DNS records updated successfully",
		Data:    records,
	})
}

// BatchDeleteRecords 批量删除DNS记录
// @Summary 批量删除DNS记录
// @Description 批量删除DNS记录
// @Tags DNS
// @Accept json
// @Produce json
// @Param request body model.BatchDeleteDNSRecordsRequest true "批量删除DNS记录请求"
// @Success 200 {object} model.APIResponse
// @Failure 400 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/records/batch [delete]
func (h *DNSHandler) BatchDeleteRecords(c *gin.Context) {
	var req model.BatchDeleteDNSRecordsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind batch delete DNS records request", "error", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}

	userID := getUserID(c)
	if err := h.dnsService.BatchDeleteRecords(c.Request.Context(), &req, userID); err != nil {
		logger.Error("Failed to batch delete DNS records", "error", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to batch delete DNS records",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "DNS records deleted successfully",
	})
}

// ImportRecords 导入DNS记录
// @Summary 导入DNS记录
// @Description 从文件导入DNS记录
// @Tags DNS
// @Accept json
// @Produce json
// @Param request body model.ImportDNSRecordsRequest true "导入DNS记录请求"
// @Success 200 {object} model.APIResponse{data=[]model.DNSRecord}
// @Failure 400 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/records/import [post]
func (h *DNSHandler) ImportRecords(c *gin.Context) {
	var req model.ImportDNSRecordsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind import DNS records request", "error", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}

	userID := getUserID(c)
	records, err := h.dnsService.ImportRecords(c.Request.Context(), &req, userID)
	if err != nil {
		logger.Error("Failed to import DNS records", "error", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to import DNS records",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "DNS records imported successfully",
		Data:    records,
	})
}

// ExportRecords 导出DNS记录
// @Summary 导出DNS记录
// @Description 导出DNS记录到文件
// @Tags DNS
// @Produce json
// @Param zone_id query string true "DNS域名ID"
// @Param format query string true "导出格式" Enums(json,csv,bind)
// @Success 200 {object} model.APIResponse{data=string}
// @Failure 400 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/records/export [get]
func (h *DNSHandler) ExportRecords(c *gin.Context) {
	var req model.ExportDNSRecordsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind export DNS records request", "error", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}

	data, err := h.dnsService.ExportRecords(c.Request.Context(), &req)
	if err != nil {
		logger.Error("Failed to export DNS records", "error", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to export DNS records",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "DNS records exported successfully",
		Data:    data,
	})
}

// ListOperationLogs 列出DNS操作日志
// @Summary 列出DNS操作日志
// @Description 分页列出DNS操作日志
// @Tags DNS
// @Produce json
// @Param zone_id query string false "DNS域名ID"
// @Param record_id query string false "DNS记录ID"
// @Param operation query string false "操作类型"
// @Param target query string false "操作目标"
// @Param status query string false "操作状态"
// @Param start_time query string false "开始时间"
// @Param end_time query string false "结束时间"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_order query string false "排序方向" default(desc)
// @Success 200 {object} model.APIResponse{data=model.ListResponse}
// @Failure 400 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/logs [get]
func (h *DNSHandler) ListOperationLogs(c *gin.Context) {
	var query model.DNSOperationLogQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		logger.Error("Failed to bind list DNS operation logs query", "error", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid query parameters",
			Details: err.Error(),
		})
		return
	}

	// 设置默认值
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.PageSize <= 0 {
		query.PageSize = 20
	}
	if query.SortBy == "" {
		query.SortBy = "created_at"
	}
	if query.SortOrder == "" {
		query.SortOrder = "desc"
	}

	logs, total, err := h.dnsService.ListOperationLogs(c.Request.Context(), &query)
	if err != nil {
		logger.Error("Failed to list DNS operation logs", "error", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to list DNS operation logs",
			Details: err.Error(),
		})
		return
	}

	totalPage := (int(total) + query.PageSize - 1) / query.PageSize
	response := model.ListResponse{
		Data: logs,
		Pagination: model.PaginationResponse{
			Page:      query.Page,
			PageSize:  query.PageSize,
			Total:     total,
			TotalPage: totalPage,
		},
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "Success",
		Data:    response,
	})
}
