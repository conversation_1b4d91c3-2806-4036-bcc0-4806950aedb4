package api

import (
	"net/http"
	"strconv"

	"cmdb-platform/internal/model"
	"cmdb-platform/internal/service"
	"cmdb-platform/pkg/logger"
	
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ResourceHandler 资源处理器
type ResourceHandler struct {
	resourceService service.ResourceService
}

// NewResourceHandler 创建资源处理器
func NewResourceHandler(resourceService service.ResourceService) *ResourceHandler {
	return &ResourceHandler{
		resourceService: resourceService,
	}
}

// CreateResource 创建资源
// @Summary 创建资源
// @Description 创建新的云资源记录
// @Tags resources
// @Accept json
// @Produce json
// @Param resource body model.CreateResourceRequest true "资源信息"
// @Success 201 {object} model.APIResponse{data=model.Resource}
// @Failure 400 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/resources [post]
func (h *ResourceHandler) CreateResource(c *gin.Context) {
	var req model.CreateResourceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}
	
	resource, err := h.resourceService.CreateResource(c.Request.Context(), &req)
	if err != nil {
		logger.Errorf("Failed to create resource: %v", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to create resource",
			Details: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, model.APIResponse{
		Code:    http.StatusCreated,
		Message: "Resource created successfully",
		Data:    resource,
	})
}

// UpdateResource 更新资源
// @Summary 更新资源
// @Description 更新云资源记录
// @Tags resources
// @Accept json
// @Produce json
// @Param id path string true "资源ID"
// @Param resource body model.UpdateResourceRequest true "资源信息"
// @Success 200 {object} model.APIResponse{data=model.Resource}
// @Failure 400 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/resources/{id} [put]
func (h *ResourceHandler) UpdateResource(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid resource ID",
		})
		return
	}
	
	var req model.UpdateResourceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}
	
	resource, err := h.resourceService.UpdateResource(c.Request.Context(), id, &req)
	if err != nil {
		logger.Errorf("Failed to update resource: %v", err)
		if err.Error() == "resource not found" {
			c.JSON(http.StatusNotFound, model.ErrorResponse{
				Code:    http.StatusNotFound,
				Message: "Resource not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to update resource",
			Details: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "Resource updated successfully",
		Data:    resource,
	})
}

// DeleteResource 删除资源
// @Summary 删除资源
// @Description 删除云资源记录
// @Tags resources
// @Produce json
// @Param id path string true "资源ID"
// @Success 200 {object} model.APIResponse
// @Failure 400 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/resources/{id} [delete]
func (h *ResourceHandler) DeleteResource(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid resource ID",
		})
		return
	}
	
	if err := h.resourceService.DeleteResource(c.Request.Context(), id); err != nil {
		logger.Errorf("Failed to delete resource: %v", err)
		if err.Error() == "resource not found" {
			c.JSON(http.StatusNotFound, model.ErrorResponse{
				Code:    http.StatusNotFound,
				Message: "Resource not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to delete resource",
			Details: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "Resource deleted successfully",
	})
}

// GetResource 获取资源详情
// @Summary 获取资源详情
// @Description 根据ID获取云资源详情
// @Tags resources
// @Produce json
// @Param id path string true "资源ID"
// @Success 200 {object} model.APIResponse{data=model.Resource}
// @Failure 400 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/resources/{id} [get]
func (h *ResourceHandler) GetResource(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid resource ID",
		})
		return
	}
	
	resource, err := h.resourceService.GetResource(c.Request.Context(), id)
	if err != nil {
		logger.Errorf("Failed to get resource: %v", err)
		if err.Error() == "resource not found" {
			c.JSON(http.StatusNotFound, model.ErrorResponse{
				Code:    http.StatusNotFound,
				Message: "Resource not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to get resource",
			Details: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "Success",
		Data:    resource,
	})
}

// ListResources 列出资源
// @Summary 列出资源
// @Description 分页查询云资源列表
// @Tags resources
// @Produce json
// @Param provider query string false "云服务商"
// @Param type query string false "资源类型"
// @Param status query string false "资源状态"
// @Param region query string false "地域"
// @Param zone query string false "可用区"
// @Param cloud_account_id query string false "云账号ID"
// @Param name query string false "资源名称"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_order query string false "排序方向" default(desc)
// @Success 200 {object} model.APIResponse{data=model.ListResponse}
// @Failure 400 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/resources [get]
func (h *ResourceHandler) ListResources(c *gin.Context) {
	var query model.ResourceQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		logger.Errorf("Failed to bind query: %v", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid query parameters",
			Details: err.Error(),
		})
		return
	}
	
	response, err := h.resourceService.ListResources(c.Request.Context(), &query)
	if err != nil {
		logger.Errorf("Failed to list resources: %v", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to list resources",
			Details: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "Success",
		Data:    response,
	})
}

// SyncResources 同步资源
// @Summary 同步资源
// @Description 从云服务商同步资源
// @Tags resources
// @Accept json
// @Produce json
// @Param sync body model.SyncTaskRequest true "同步请求"
// @Success 202 {object} model.APIResponse{data=model.SyncTask}
// @Failure 400 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/resources/sync [post]
func (h *ResourceHandler) SyncResources(c *gin.Context) {
	var req model.SyncTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}
	
	syncTask, err := h.resourceService.SyncResources(c.Request.Context(), &req)
	if err != nil {
		logger.Errorf("Failed to sync resources: %v", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to sync resources",
			Details: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusAccepted, model.APIResponse{
		Code:    http.StatusAccepted,
		Message: "Sync task created successfully",
		Data:    syncTask,
	})
}

// GetResourceStats 获取资源统计
// @Summary 获取资源统计
// @Description 获取资源统计信息
// @Tags resources
// @Produce json
// @Success 200 {object} model.APIResponse{data=model.StatsResponse}
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/resources/stats [get]
func (h *ResourceHandler) GetResourceStats(c *gin.Context) {
	// TODO: 从上下文中获取租户ID
	tenantID := uuid.New() // 临时使用
	
	stats, err := h.resourceService.GetResourceStats(c.Request.Context(), tenantID)
	if err != nil {
		logger.Errorf("Failed to get resource stats: %v", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to get resource stats",
			Details: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "Success",
		Data:    stats,
	})
}
