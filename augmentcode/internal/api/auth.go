package api

import (
	"net/http"
	"strconv"

	"cmdb-platform/internal/middleware"
	"cmdb-platform/internal/model"
	"cmdb-platform/internal/service"
	"cmdb-platform/pkg/logger"
	
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	authService service.AuthService
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(authService service.AuthService) *AuthHandler {
	return &AuthHandler{
		authService: authService,
	}
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户登录获取访问令牌
// @Tags auth
// @Accept json
// @Produce json
// @Param login body model.LoginRequest true "登录信息"
// @Success 200 {object} model.APIResponse{data=model.LoginResponse}
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var req model.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind login request: %v", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}
	
	response, err := h.authService.Login(c.Request.Context(), &req)
	if err != nil {
		logger.Errorf("Login failed: %v", err)
		c.JSON(http.StatusUnauthorized, model.ErrorResponse{
			Code:    http.StatusUnauthorized,
			Message: "Login failed",
			Details: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "Login successful",
		Data:    response,
	})
}

// RefreshToken 刷新令牌
// @Summary 刷新令牌
// @Description 使用刷新令牌获取新的访问令牌
// @Tags auth
// @Accept json
// @Produce json
// @Param refresh_token body object{refresh_token=string} true "刷新令牌"
// @Success 200 {object} model.APIResponse{data=model.LoginResponse}
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req struct {
		RefreshToken string `json:"refresh_token" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind refresh token request: %v", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}
	
	response, err := h.authService.RefreshToken(c.Request.Context(), req.RefreshToken)
	if err != nil {
		logger.Errorf("Refresh token failed: %v", err)
		c.JSON(http.StatusUnauthorized, model.ErrorResponse{
			Code:    http.StatusUnauthorized,
			Message: "Refresh token failed",
			Details: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "Token refreshed successfully",
		Data:    response,
	})
}

// Logout 用户登出
// @Summary 用户登出
// @Description 用户登出并清除会话
// @Tags auth
// @Produce json
// @Security BearerAuth
// @Success 200 {object} model.APIResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	userID, err := middleware.GetCurrentUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, model.ErrorResponse{
			Code:    http.StatusUnauthorized,
			Message: "User not authenticated",
		})
		return
	}
	
	// 从请求头中获取令牌
	token := c.GetHeader("Authorization")
	if token != "" && len(token) > 7 {
		token = token[7:] // 移除 "Bearer " 前缀
	}
	
	if err := h.authService.Logout(c.Request.Context(), userID, token); err != nil {
		logger.Errorf("Logout failed: %v", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Logout failed",
			Details: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "Logout successful",
	})
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 修改当前用户密码
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param password body model.ChangePasswordRequest true "密码信息"
// @Success 200 {object} model.APIResponse
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/auth/change-password [post]
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	userID, err := middleware.GetCurrentUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, model.ErrorResponse{
			Code:    http.StatusUnauthorized,
			Message: "User not authenticated",
		})
		return
	}
	
	var req model.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind change password request: %v", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}
	
	if err := h.authService.ChangePassword(c.Request.Context(), userID, &req); err != nil {
		logger.Errorf("Change password failed: %v", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Change password failed",
			Details: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "Password changed successfully",
	})
}

// GetProfile 获取用户资料
// @Summary 获取用户资料
// @Description 获取当前用户的详细信息
// @Tags auth
// @Produce json
// @Security BearerAuth
// @Success 200 {object} model.APIResponse{data=model.User}
// @Failure 401 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/auth/profile [get]
func (h *AuthHandler) GetProfile(c *gin.Context) {
	userID, err := middleware.GetCurrentUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, model.ErrorResponse{
			Code:    http.StatusUnauthorized,
			Message: "User not authenticated",
		})
		return
	}
	
	user, err := h.authService.GetUser(c.Request.Context(), userID)
	if err != nil {
		logger.Errorf("Get profile failed: %v", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to get user profile",
			Details: err.Error(),
		})
		return
	}
	
	// 清除敏感信息
	user.Password = ""
	
	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "Success",
		Data:    user,
	})
}

// CreateUser 创建用户
// @Summary 创建用户
// @Description 创建新用户（需要管理员权限）
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param user body model.CreateUserRequest true "用户信息"
// @Success 201 {object} model.APIResponse{data=model.User}
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/users [post]
func (h *AuthHandler) CreateUser(c *gin.Context) {
	var req model.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind create user request: %v", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}
	
	user, err := h.authService.CreateUser(c.Request.Context(), &req)
	if err != nil {
		logger.Errorf("Create user failed: %v", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Create user failed",
			Details: err.Error(),
		})
		return
	}
	
	// 清除敏感信息
	user.Password = ""
	
	c.JSON(http.StatusCreated, model.APIResponse{
		Code:    http.StatusCreated,
		Message: "User created successfully",
		Data:    user,
	})
}

// UpdateUser 更新用户
// @Summary 更新用户
// @Description 更新用户信息（需要管理员权限）
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "用户ID"
// @Param user body model.UpdateUserRequest true "用户信息"
// @Success 200 {object} model.APIResponse{data=model.User}
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/users/{id} [put]
func (h *AuthHandler) UpdateUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid user ID",
		})
		return
	}
	
	var req model.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind update user request: %v", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}
	
	user, err := h.authService.UpdateUser(c.Request.Context(), id, &req)
	if err != nil {
		logger.Errorf("Update user failed: %v", err)
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, model.ErrorResponse{
				Code:    http.StatusNotFound,
				Message: "User not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Update user failed",
			Details: err.Error(),
		})
		return
	}
	
	// 清除敏感信息
	user.Password = ""
	
	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "User updated successfully",
		Data:    user,
	})
}

// ListUsers 列出用户
// @Summary 列出用户
// @Description 分页查询用户列表（需要管理员权限）
// @Tags users
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Success 200 {object} model.APIResponse{data=model.ListResponse}
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/users [get]
func (h *AuthHandler) ListUsers(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	
	response, err := h.authService.ListUsers(c.Request.Context(), page, pageSize)
	if err != nil {
		logger.Errorf("List users failed: %v", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to list users",
			Details: err.Error(),
		})
		return
	}
	
	// 清除敏感信息
	if users, ok := response.Data.([]model.User); ok {
		for i := range users {
			users[i].Password = ""
		}
		response.Data = users
	}
	
	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "Success",
		Data:    response,
	})
}
