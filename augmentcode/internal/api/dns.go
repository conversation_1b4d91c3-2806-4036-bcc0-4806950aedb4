package api

import (
	"net/http"

	"cmdb-platform/internal/model"
	"cmdb-platform/internal/service"
	"cmdb-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// DNSHandler DNS管理处理器
type DNSHandler struct {
	dnsService service.DNSService
}

// NewDNSHandler 创建DNS管理处理器
func NewDNSHandler(dnsService service.DNSService) *DNSHandler {
	return &DNSHandler{
		dnsService: dnsService,
	}
}

// CreateZone 创建DNS域名
// @Summary 创建DNS域名
// @Description 在指定云账户中创建DNS域名
// @Tags DNS
// @Accept json
// @Produce json
// @Param request body model.CreateDNSZoneRequest true "创建DNS域名请求"
// @Success 200 {object} model.APIResponse{data=model.DNSZone}
// @Failure 400 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/zones [post]
func (h *DNSHandler) CreateZone(c *gin.Context) {
	var req model.CreateDNSZoneRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind create DNS zone request", "error", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}

	userID := getUserID(c)
	zone, err := h.dnsService.CreateZone(c.Request.Context(), &req, userID)
	if err != nil {
		logger.Error("Failed to create DNS zone", "error", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to create DNS zone",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "DNS zone created successfully",
		Data:    zone,
	})
}

// GetZone 获取DNS域名详情
// @Summary 获取DNS域名详情
// @Description 根据ID获取DNS域名详情
// @Tags DNS
// @Produce json
// @Param id path string true "DNS域名ID"
// @Success 200 {object} model.APIResponse{data=model.DNSZone}
// @Failure 400 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/zones/{id} [get]
func (h *DNSHandler) GetZone(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid zone ID",
			Details: err.Error(),
		})
		return
	}

	zone, err := h.dnsService.GetZone(c.Request.Context(), id)
	if err != nil {
		logger.Error("Failed to get DNS zone", "error", err, "id", id)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to get DNS zone",
			Details: err.Error(),
		})
		return
	}

	if zone == nil {
		c.JSON(http.StatusNotFound, model.ErrorResponse{
			Code:    http.StatusNotFound,
			Message: "DNS zone not found",
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "Success",
		Data:    zone,
	})
}

// UpdateZone 更新DNS域名
// @Summary 更新DNS域名
// @Description 更新DNS域名信息
// @Tags DNS
// @Accept json
// @Produce json
// @Param id path string true "DNS域名ID"
// @Param request body model.UpdateDNSZoneRequest true "更新DNS域名请求"
// @Success 200 {object} model.APIResponse{data=model.DNSZone}
// @Failure 400 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/zones/{id} [put]
func (h *DNSHandler) UpdateZone(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid zone ID",
			Details: err.Error(),
		})
		return
	}

	var req model.UpdateDNSZoneRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind update DNS zone request", "error", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}

	userID := getUserID(c)
	zone, err := h.dnsService.UpdateZone(c.Request.Context(), id, &req, userID)
	if err != nil {
		logger.Error("Failed to update DNS zone", "error", err, "id", id)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to update DNS zone",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "DNS zone updated successfully",
		Data:    zone,
	})
}

// DeleteZone 删除DNS域名
// @Summary 删除DNS域名
// @Description 删除DNS域名
// @Tags DNS
// @Produce json
// @Param id path string true "DNS域名ID"
// @Success 200 {object} model.APIResponse
// @Failure 400 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/zones/{id} [delete]
func (h *DNSHandler) DeleteZone(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid zone ID",
			Details: err.Error(),
		})
		return
	}

	userID := getUserID(c)
	if err := h.dnsService.DeleteZone(c.Request.Context(), id, userID); err != nil {
		logger.Error("Failed to delete DNS zone", "error", err, "id", id)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to delete DNS zone",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "DNS zone deleted successfully",
	})
}

// ListZones 列出DNS域名
// @Summary 列出DNS域名
// @Description 分页列出DNS域名
// @Tags DNS
// @Produce json
// @Param provider query string false "云服务商"
// @Param cloud_account_id query string false "云账户ID"
// @Param domain_name query string false "域名"
// @Param status query string false "状态"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_order query string false "排序方向" default(desc)
// @Success 200 {object} model.APIResponse{data=model.ListResponse}
// @Failure 400 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/zones [get]
func (h *DNSHandler) ListZones(c *gin.Context) {
	var query model.DNSZoneQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		logger.Error("Failed to bind list DNS zones query", "error", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid query parameters",
			Details: err.Error(),
		})
		return
	}

	// 设置默认值
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.PageSize <= 0 {
		query.PageSize = 20
	}
	if query.SortBy == "" {
		query.SortBy = "created_at"
	}
	if query.SortOrder == "" {
		query.SortOrder = "desc"
	}

	zones, total, err := h.dnsService.ListZones(c.Request.Context(), &query)
	if err != nil {
		logger.Error("Failed to list DNS zones", "error", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to list DNS zones",
			Details: err.Error(),
		})
		return
	}

	totalPage := (int(total) + query.PageSize - 1) / query.PageSize
	response := model.ListResponse{
		Data: zones,
		Pagination: model.PaginationResponse{
			Page:      query.Page,
			PageSize:  query.PageSize,
			Total:     total,
			TotalPage: totalPage,
		},
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "Success",
		Data:    response,
	})
}

// SyncZones 同步DNS域名
// @Summary 同步DNS域名
// @Description 从云服务商同步DNS域名到本地
// @Tags DNS
// @Produce json
// @Param cloud_account_id query string true "云账户ID"
// @Success 200 {object} model.APIResponse
// @Failure 400 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/zones/sync [post]
func (h *DNSHandler) SyncZones(c *gin.Context) {
	cloudAccountIDStr := c.Query("cloud_account_id")
	if cloudAccountIDStr == "" {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "cloud_account_id is required",
		})
		return
	}

	cloudAccountID, err := uuid.Parse(cloudAccountIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid cloud account ID",
			Details: err.Error(),
		})
		return
	}

	userID := getUserID(c)
	if err := h.dnsService.SyncZones(c.Request.Context(), cloudAccountID, userID); err != nil {
		logger.Error("Failed to sync DNS zones", "error", err, "cloudAccountID", cloudAccountID)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to sync DNS zones",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "DNS zones synced successfully",
	})
}

// CreateRecord 创建DNS记录
// @Summary 创建DNS记录
// @Description 在指定DNS域名中创建DNS记录
// @Tags DNS
// @Accept json
// @Produce json
// @Param request body model.CreateDNSRecordRequest true "创建DNS记录请求"
// @Success 200 {object} model.APIResponse{data=model.DNSRecord}
// @Failure 400 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/records [post]
func (h *DNSHandler) CreateRecord(c *gin.Context) {
	var req model.CreateDNSRecordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind create DNS record request", "error", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}

	userID := getUserID(c)
	record, err := h.dnsService.CreateRecord(c.Request.Context(), &req, userID)
	if err != nil {
		logger.Error("Failed to create DNS record", "error", err)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to create DNS record",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "DNS record created successfully",
		Data:    record,
	})
}

// GetRecord 获取DNS记录详情
// @Summary 获取DNS记录详情
// @Description 根据ID获取DNS记录详情
// @Tags DNS
// @Produce json
// @Param id path string true "DNS记录ID"
// @Success 200 {object} model.APIResponse{data=model.DNSRecord}
// @Failure 400 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/records/{id} [get]
func (h *DNSHandler) GetRecord(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid record ID",
			Details: err.Error(),
		})
		return
	}

	record, err := h.dnsService.GetRecord(c.Request.Context(), id)
	if err != nil {
		logger.Error("Failed to get DNS record", "error", err, "id", id)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to get DNS record",
			Details: err.Error(),
		})
		return
	}

	if record == nil {
		c.JSON(http.StatusNotFound, model.ErrorResponse{
			Code:    http.StatusNotFound,
			Message: "DNS record not found",
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "Success",
		Data:    record,
	})
}

// UpdateRecord 更新DNS记录
// @Summary 更新DNS记录
// @Description 更新DNS记录信息
// @Tags DNS
// @Accept json
// @Produce json
// @Param id path string true "DNS记录ID"
// @Param request body model.UpdateDNSRecordRequest true "更新DNS记录请求"
// @Success 200 {object} model.APIResponse{data=model.DNSRecord}
// @Failure 400 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/records/{id} [put]
func (h *DNSHandler) UpdateRecord(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid record ID",
			Details: err.Error(),
		})
		return
	}

	var req model.UpdateDNSRecordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind update DNS record request", "error", err)
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}

	userID := getUserID(c)
	record, err := h.dnsService.UpdateRecord(c.Request.Context(), id, &req, userID)
	if err != nil {
		logger.Error("Failed to update DNS record", "error", err, "id", id)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to update DNS record",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "DNS record updated successfully",
		Data:    record,
	})
}

// DeleteRecord 删除DNS记录
// @Summary 删除DNS记录
// @Description 删除DNS记录
// @Tags DNS
// @Produce json
// @Param id path string true "DNS记录ID"
// @Success 200 {object} model.APIResponse
// @Failure 400 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/dns/records/{id} [delete]
func (h *DNSHandler) DeleteRecord(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid record ID",
			Details: err.Error(),
		})
		return
	}

	userID := getUserID(c)
	if err := h.dnsService.DeleteRecord(c.Request.Context(), id, userID); err != nil {
		logger.Error("Failed to delete DNS record", "error", err, "id", id)
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to delete DNS record",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Code:    http.StatusOK,
		Message: "DNS record deleted successfully",
	})
}

// getUserID 从上下文获取用户ID
func getUserID(c *gin.Context) uuid.UUID {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(uuid.UUID); ok {
			return id
		}
	}
	// 返回默认用户ID或生成新的UUID
	return uuid.New()
}
