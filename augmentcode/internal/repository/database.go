package repository

import (
	"fmt"
	"time"

	"cmdb-platform/internal/config"
	"cmdb-platform/internal/model"
	"cmdb-platform/pkg/logger"
	
	"github.com/go-redis/redis/v8"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
)

// Database 数据库管理器
type Database struct {
	DB    *gorm.DB
	Redis *redis.Client
}

// NewDatabase 创建数据库连接
func NewDatabase(cfg *config.Config) (*Database, error) {
	// 连接PostgreSQL
	db, err := connectPostgreSQL(&cfg.Database.Postgres)
	if err != nil {
		return nil, fmt.Errorf("failed to connect PostgreSQL: %w", err)
	}
	
	// 连接Redis
	rdb, err := connectRedis(&cfg.Database.Redis)
	if err != nil {
		return nil, fmt.Errorf("failed to connect Redis: %w", err)
	}
	
	database := &Database{
		DB:    db,
		Redis: rdb,
	}
	
	// 自动迁移数据库表
	if err := database.AutoMigrate(); err != nil {
		return nil, fmt.Errorf("failed to auto migrate: %w", err)
	}
	
	return database, nil
}

// connectPostgreSQL 连接PostgreSQL数据库
func connectPostgreSQL(cfg *config.PostgresConfig) (*gorm.DB, error) {
	dsn := cfg.GetDSN()
	
	// 配置GORM日志
	var gormLogLevel gormLogger.LogLevel
	switch logger.GetLogger().Level {
	case logger.GetLogger().Level:
		gormLogLevel = gormLogger.Info
	default:
		gormLogLevel = gormLogger.Warn
	}
	
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger.Default.LogMode(gormLogLevel),
	})
	if err != nil {
		return nil, err
	}
	
	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}
	
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(cfg.ConnMaxLifetime)
	
	return db, nil
}

// connectRedis 连接Redis
func connectRedis(cfg *config.RedisConfig) (*redis.Client, error) {
	rdb := redis.NewClient(&redis.Options{
		Addr:         cfg.GetRedisAddr(),
		Password:     cfg.Password,
		DB:           cfg.DB,
		PoolSize:     cfg.PoolSize,
		MinIdleConns: cfg.MinIdleConns,
	})
	
	return rdb, nil
}

// AutoMigrate 自动迁移数据库表
func (d *Database) AutoMigrate() error {
	logger.Info("Starting database migration...")
	
	// 用户相关表
	if err := d.DB.AutoMigrate(
		&model.User{},
		&model.Tenant{},
		&model.UserTenant{},
		&model.CloudAccount{},
		&model.Permission{},
		&model.Role{},
		&model.UserSession{},
		&model.AuditLog{},
	); err != nil {
		return fmt.Errorf("failed to migrate user tables: %w", err)
	}
	
	// 资源相关表
	if err := d.DB.AutoMigrate(
		&model.Resource{},
		&model.ConfigItem{},
		&model.ResourceRelation{},
		&model.ChangeRecord{},
		&model.Alert{},
		&model.SyncTask{},
		&model.ResourceTemplate{},
		&model.ResourceGroup{},
	); err != nil {
		return fmt.Errorf("failed to migrate resource tables: %w", err)
	}
	
	logger.Info("Database migration completed successfully")
	return nil
}

// Close 关闭数据库连接
func (d *Database) Close() error {
	// 关闭PostgreSQL连接
	if sqlDB, err := d.DB.DB(); err == nil {
		if err := sqlDB.Close(); err != nil {
			logger.Errorf("Failed to close PostgreSQL connection: %v", err)
		}
	}
	
	// 关闭Redis连接
	if err := d.Redis.Close(); err != nil {
		logger.Errorf("Failed to close Redis connection: %v", err)
	}
	
	return nil
}

// Health 检查数据库健康状态
func (d *Database) Health() map[string]string {
	status := make(map[string]string)
	
	// 检查PostgreSQL
	if sqlDB, err := d.DB.DB(); err != nil {
		status["postgres"] = "error"
	} else if err := sqlDB.Ping(); err != nil {
		status["postgres"] = "error"
	} else {
		status["postgres"] = "healthy"
	}
	
	// 检查Redis
	if err := d.Redis.Ping(d.Redis.Context()).Err(); err != nil {
		status["redis"] = "error"
	} else {
		status["redis"] = "healthy"
	}
	
	return status
}

// Transaction 执行事务
func (d *Database) Transaction(fn func(*gorm.DB) error) error {
	return d.DB.Transaction(fn)
}

// GetDB 获取数据库连接
func (d *Database) GetDB() *gorm.DB {
	return d.DB
}

// GetRedis 获取Redis连接
func (d *Database) GetRedis() *redis.Client {
	return d.Redis
}

// CreateIndexes 创建索引
func (d *Database) CreateIndexes() error {
	logger.Info("Creating database indexes...")
	
	// 创建复合索引
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_resources_provider_type ON resources(provider, type)",
		"CREATE INDEX IF NOT EXISTS idx_resources_cloud_account_id ON resources(cloud_account_id)",
		"CREATE INDEX IF NOT EXISTS idx_resources_tenant_id ON resources(tenant_id)",
		"CREATE INDEX IF NOT EXISTS idx_resources_status ON resources(status)",
		"CREATE INDEX IF NOT EXISTS idx_resources_region ON resources(region)",
		"CREATE INDEX IF NOT EXISTS idx_config_items_resource_id ON config_items(resource_id)",
		"CREATE INDEX IF NOT EXISTS idx_resource_relations_source ON resource_relations(source_resource_id)",
		"CREATE INDEX IF NOT EXISTS idx_resource_relations_target ON resource_relations(target_resource_id)",
		"CREATE INDEX IF NOT EXISTS idx_change_records_resource_id ON change_records(resource_id)",
		"CREATE INDEX IF NOT EXISTS idx_alerts_resource_id ON alerts(resource_id)",
		"CREATE INDEX IF NOT EXISTS idx_alerts_status ON alerts(status)",
		"CREATE INDEX IF NOT EXISTS idx_sync_tasks_cloud_account_id ON sync_tasks(cloud_account_id)",
		"CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_audit_logs_tenant_id ON audit_logs(tenant_id)",
	}
	
	for _, index := range indexes {
		if err := d.DB.Exec(index).Error; err != nil {
			logger.Warnf("Failed to create index: %s, error: %v", index, err)
		}
	}
	
	logger.Info("Database indexes created successfully")
	return nil
}

// InitDefaultData 初始化默认数据
func (d *Database) InitDefaultData() error {
	logger.Info("Initializing default data...")
	
	// 创建默认租户
	defaultTenant := &model.Tenant{
		BaseModel: model.BaseModel{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Name:        "Default Tenant",
		Code:        "default",
		Description: "Default tenant for system",
		Status:      "active",
	}
	
	if err := d.DB.FirstOrCreate(defaultTenant, "code = ?", "default").Error; err != nil {
		return fmt.Errorf("failed to create default tenant: %w", err)
	}
	
	// 创建默认管理员用户
	defaultUser := &model.User{
		BaseModel: model.BaseModel{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Username:    "admin",
		Email:       "<EMAIL>",
		Password:    "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
		DisplayName: "Administrator",
		Role:        model.UserRoleAdmin,
		Status:      model.UserStatusActive,
	}
	
	if err := d.DB.FirstOrCreate(defaultUser, "username = ?", "admin").Error; err != nil {
		return fmt.Errorf("failed to create default user: %w", err)
	}
	
	// 创建用户租户关联
	userTenant := &model.UserTenant{
		BaseModel: model.BaseModel{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		UserID:   defaultUser.ID,
		TenantID: defaultTenant.ID,
		Role:     model.UserRoleAdmin,
	}
	
	if err := d.DB.FirstOrCreate(userTenant, "user_id = ? AND tenant_id = ?", defaultUser.ID, defaultTenant.ID).Error; err != nil {
		return fmt.Errorf("failed to create user tenant relation: %w", err)
	}
	
	logger.Info("Default data initialized successfully")
	return nil
}
