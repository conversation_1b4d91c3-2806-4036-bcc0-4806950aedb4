package repository

import (
	"context"
	"fmt"

	"cmdb-platform/internal/model"
	"cmdb-platform/pkg/logger"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// DNSZoneRepository DNS域名仓库接口
type DNSZoneRepository interface {
	Create(ctx context.Context, zone *model.DNSZone) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.DNSZone, error)
	GetByDomainName(ctx context.Context, tenantID uuid.UUID, domainName string) (*model.DNSZone, error)
	Update(ctx context.Context, zone *model.DNSZone) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, query *model.DNSZoneQuery) ([]*model.DNSZone, int64, error)
	ListByCloudAccount(ctx context.Context, cloudAccountID uuid.UUID) ([]*model.DNSZone, error)
}

// DNSRecordRepository DNS记录仓库接口
type DNSRecordRepository interface {
	Create(ctx context.Context, record *model.DNSRecord) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.DNSRecord, error)
	Update(ctx context.Context, record *model.DNSRecord) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, query *model.DNSRecordQuery) ([]*model.DNSRecord, int64, error)
	ListByZone(ctx context.Context, zoneID uuid.UUID) ([]*model.DNSRecord, error)
	BatchCreate(ctx context.Context, records []*model.DNSRecord) error
	BatchUpdate(ctx context.Context, records []*model.DNSRecord) error
	BatchDelete(ctx context.Context, ids []uuid.UUID) error
}

// DNSOperationLogRepository DNS操作日志仓库接口
type DNSOperationLogRepository interface {
	Create(ctx context.Context, log *model.DNSOperationLog) error
	List(ctx context.Context, query *model.DNSOperationLogQuery) ([]*model.DNSOperationLog, int64, error)
}

// dnsZoneRepository DNS域名仓库实现
type dnsZoneRepository struct {
	db *gorm.DB
}

// NewDNSZoneRepository 创建DNS域名仓库
func NewDNSZoneRepository(db *gorm.DB) DNSZoneRepository {
	return &dnsZoneRepository{db: db}
}

// Create 创建DNS域名
func (r *dnsZoneRepository) Create(ctx context.Context, zone *model.DNSZone) error {
	if err := r.db.WithContext(ctx).Create(zone).Error; err != nil {
		logger.Error("Failed to create DNS zone", "error", err, "domain", zone.DomainName)
		return fmt.Errorf("failed to create DNS zone: %w", err)
	}
	return nil
}

// GetByID 根据ID获取DNS域名
func (r *dnsZoneRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.DNSZone, error) {
	var zone model.DNSZone
	if err := r.db.WithContext(ctx).Preload("CloudAccount").First(&zone, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Error("Failed to get DNS zone by ID", "error", err, "id", id)
		return nil, fmt.Errorf("failed to get DNS zone: %w", err)
	}
	return &zone, nil
}

// GetByDomainName 根据域名获取DNS域名
func (r *dnsZoneRepository) GetByDomainName(ctx context.Context, tenantID uuid.UUID, domainName string) (*model.DNSZone, error) {
	var zone model.DNSZone
	if err := r.db.WithContext(ctx).Preload("CloudAccount").
		Where("tenant_id = ? AND domain_name = ?", tenantID, domainName).
		First(&zone).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Error("Failed to get DNS zone by domain name", "error", err, "domain", domainName)
		return nil, fmt.Errorf("failed to get DNS zone: %w", err)
	}
	return &zone, nil
}

// Update 更新DNS域名
func (r *dnsZoneRepository) Update(ctx context.Context, zone *model.DNSZone) error {
	if err := r.db.WithContext(ctx).Save(zone).Error; err != nil {
		logger.Error("Failed to update DNS zone", "error", err, "id", zone.ID)
		return fmt.Errorf("failed to update DNS zone: %w", err)
	}
	return nil
}

// Delete 删除DNS域名
func (r *dnsZoneRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&model.DNSZone{}, "id = ?", id).Error; err != nil {
		logger.Error("Failed to delete DNS zone", "error", err, "id", id)
		return fmt.Errorf("failed to delete DNS zone: %w", err)
	}
	return nil
}

// List 列出DNS域名
func (r *dnsZoneRepository) List(ctx context.Context, query *model.DNSZoneQuery) ([]*model.DNSZone, int64, error) {
	var zones []*model.DNSZone
	var total int64

	db := r.db.WithContext(ctx).Model(&model.DNSZone{})

	// 添加查询条件
	if query.Provider != "" {
		db = db.Where("provider = ?", query.Provider)
	}
	if query.CloudAccountID != uuid.Nil {
		db = db.Where("cloud_account_id = ?", query.CloudAccountID)
	}
	if query.DomainName != "" {
		db = db.Where("domain_name ILIKE ?", "%"+query.DomainName+"%")
	}
	if query.Status != "" {
		db = db.Where("status = ?", query.Status)
	}

	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		logger.Error("Failed to count DNS zones", "error", err)
		return nil, 0, fmt.Errorf("failed to count DNS zones: %w", err)
	}

	// 添加排序和分页
	offset := (query.Page - 1) * query.PageSize
	if err := db.Preload("CloudAccount").
		Order(fmt.Sprintf("%s %s", query.SortBy, query.SortOrder)).
		Offset(offset).Limit(query.PageSize).
		Find(&zones).Error; err != nil {
		logger.Error("Failed to list DNS zones", "error", err)
		return nil, 0, fmt.Errorf("failed to list DNS zones: %w", err)
	}

	return zones, total, nil
}

// ListByCloudAccount 根据云账户列出DNS域名
func (r *dnsZoneRepository) ListByCloudAccount(ctx context.Context, cloudAccountID uuid.UUID) ([]*model.DNSZone, error) {
	var zones []*model.DNSZone
	if err := r.db.WithContext(ctx).Preload("CloudAccount").
		Where("cloud_account_id = ?", cloudAccountID).
		Find(&zones).Error; err != nil {
		logger.Error("Failed to list DNS zones by cloud account", "error", err, "cloudAccountID", cloudAccountID)
		return nil, fmt.Errorf("failed to list DNS zones: %w", err)
	}
	return zones, nil
}

// dnsRecordRepository DNS记录仓库实现
type dnsRecordRepository struct {
	db *gorm.DB
}

// NewDNSRecordRepository 创建DNS记录仓库
func NewDNSRecordRepository(db *gorm.DB) DNSRecordRepository {
	return &dnsRecordRepository{db: db}
}

// Create 创建DNS记录
func (r *dnsRecordRepository) Create(ctx context.Context, record *model.DNSRecord) error {
	if err := r.db.WithContext(ctx).Create(record).Error; err != nil {
		logger.Error("Failed to create DNS record", "error", err, "name", record.Name)
		return fmt.Errorf("failed to create DNS record: %w", err)
	}
	return nil
}

// GetByID 根据ID获取DNS记录
func (r *dnsRecordRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.DNSRecord, error) {
	var record model.DNSRecord
	if err := r.db.WithContext(ctx).Preload("Zone").First(&record, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Error("Failed to get DNS record by ID", "error", err, "id", id)
		return nil, fmt.Errorf("failed to get DNS record: %w", err)
	}
	return &record, nil
}

// Update 更新DNS记录
func (r *dnsRecordRepository) Update(ctx context.Context, record *model.DNSRecord) error {
	if err := r.db.WithContext(ctx).Save(record).Error; err != nil {
		logger.Error("Failed to update DNS record", "error", err, "id", record.ID)
		return fmt.Errorf("failed to update DNS record: %w", err)
	}
	return nil
}

// Delete 删除DNS记录
func (r *dnsRecordRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&model.DNSRecord{}, "id = ?", id).Error; err != nil {
		logger.Error("Failed to delete DNS record", "error", err, "id", id)
		return fmt.Errorf("failed to delete DNS record: %w", err)
	}
	return nil
}

// List 列出DNS记录
func (r *dnsRecordRepository) List(ctx context.Context, query *model.DNSRecordQuery) ([]*model.DNSRecord, int64, error) {
	var records []*model.DNSRecord
	var total int64

	db := r.db.WithContext(ctx).Model(&model.DNSRecord{})

	// 添加查询条件
	if query.ZoneID != uuid.Nil {
		db = db.Where("zone_id = ?", query.ZoneID)
	}
	if query.Name != "" {
		db = db.Where("name ILIKE ?", "%"+query.Name+"%")
	}
	if query.Type != "" {
		db = db.Where("type = ?", query.Type)
	}
	if query.Value != "" {
		db = db.Where("value ILIKE ?", "%"+query.Value+"%")
	}
	if query.Status != "" {
		db = db.Where("status = ?", query.Status)
	}

	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		logger.Error("Failed to count DNS records", "error", err)
		return nil, 0, fmt.Errorf("failed to count DNS records: %w", err)
	}

	// 添加排序和分页
	offset := (query.Page - 1) * query.PageSize
	if err := db.Preload("Zone").
		Order(fmt.Sprintf("%s %s", query.SortBy, query.SortOrder)).
		Offset(offset).Limit(query.PageSize).
		Find(&records).Error; err != nil {
		logger.Error("Failed to list DNS records", "error", err)
		return nil, 0, fmt.Errorf("failed to list DNS records: %w", err)
	}

	return records, total, nil
}

// ListByZone 根据域名列出DNS记录
func (r *dnsRecordRepository) ListByZone(ctx context.Context, zoneID uuid.UUID) ([]*model.DNSRecord, error) {
	var records []*model.DNSRecord
	if err := r.db.WithContext(ctx).Preload("Zone").
		Where("zone_id = ?", zoneID).
		Find(&records).Error; err != nil {
		logger.Error("Failed to list DNS records by zone", "error", err, "zoneID", zoneID)
		return nil, fmt.Errorf("failed to list DNS records: %w", err)
	}
	return records, nil
}

// BatchCreate 批量创建DNS记录
func (r *dnsRecordRepository) BatchCreate(ctx context.Context, records []*model.DNSRecord) error {
	if err := r.db.WithContext(ctx).CreateInBatches(records, 100).Error; err != nil {
		logger.Error("Failed to batch create DNS records", "error", err, "count", len(records))
		return fmt.Errorf("failed to batch create DNS records: %w", err)
	}
	return nil
}

// BatchUpdate 批量更新DNS记录
func (r *dnsRecordRepository) BatchUpdate(ctx context.Context, records []*model.DNSRecord) error {
	tx := r.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, record := range records {
		if err := tx.Save(record).Error; err != nil {
			tx.Rollback()
			logger.Error("Failed to batch update DNS records", "error", err, "recordID", record.ID)
			return fmt.Errorf("failed to batch update DNS records: %w", err)
		}
	}

	if err := tx.Commit().Error; err != nil {
		logger.Error("Failed to commit batch update DNS records", "error", err)
		return fmt.Errorf("failed to commit batch update: %w", err)
	}

	return nil
}

// BatchDelete 批量删除DNS记录
func (r *dnsRecordRepository) BatchDelete(ctx context.Context, ids []uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&model.DNSRecord{}, "id IN ?", ids).Error; err != nil {
		logger.Error("Failed to batch delete DNS records", "error", err, "count", len(ids))
		return fmt.Errorf("failed to batch delete DNS records: %w", err)
	}
	return nil
}

// dnsOperationLogRepository DNS操作日志仓库实现
type dnsOperationLogRepository struct {
	db *gorm.DB
}

// NewDNSOperationLogRepository 创建DNS操作日志仓库
func NewDNSOperationLogRepository(db *gorm.DB) DNSOperationLogRepository {
	return &dnsOperationLogRepository{db: db}
}

// Create 创建DNS操作日志
func (r *dnsOperationLogRepository) Create(ctx context.Context, log *model.DNSOperationLog) error {
	if err := r.db.WithContext(ctx).Create(log).Error; err != nil {
		logger.Error("Failed to create DNS operation log", "error", err, "operation", log.Operation)
		return fmt.Errorf("failed to create DNS operation log: %w", err)
	}
	return nil
}

// List 列出DNS操作日志
func (r *dnsOperationLogRepository) List(ctx context.Context, query *model.DNSOperationLogQuery) ([]*model.DNSOperationLog, int64, error) {
	var logs []*model.DNSOperationLog
	var total int64

	db := r.db.WithContext(ctx).Model(&model.DNSOperationLog{})

	// 添加查询条件
	if query.ZoneID != nil {
		db = db.Where("zone_id = ?", *query.ZoneID)
	}
	if query.RecordID != nil {
		db = db.Where("record_id = ?", *query.RecordID)
	}
	if query.Operation != "" {
		db = db.Where("operation = ?", query.Operation)
	}
	if query.Target != "" {
		db = db.Where("target = ?", query.Target)
	}
	if query.Status != "" {
		db = db.Where("status = ?", query.Status)
	}
	if !query.StartTime.IsZero() {
		db = db.Where("created_at >= ?", query.StartTime)
	}
	if !query.EndTime.IsZero() {
		db = db.Where("created_at <= ?", query.EndTime)
	}

	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		logger.Error("Failed to count DNS operation logs", "error", err)
		return nil, 0, fmt.Errorf("failed to count DNS operation logs: %w", err)
	}

	// 添加排序和分页
	offset := (query.Page - 1) * query.PageSize
	if err := db.Preload("Zone").Preload("Record").
		Order(fmt.Sprintf("%s %s", query.SortBy, query.SortOrder)).
		Offset(offset).Limit(query.PageSize).
		Find(&logs).Error; err != nil {
		logger.Error("Failed to list DNS operation logs", "error", err)
		return nil, 0, fmt.Errorf("failed to list DNS operation logs: %w", err)
	}

	return logs, total, nil
}
