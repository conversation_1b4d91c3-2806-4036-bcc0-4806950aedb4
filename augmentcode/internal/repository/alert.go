package repository

import (
	"context"

	"cmdb-platform/internal/model"
	"cmdb-platform/pkg/logger"
	
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AlertRepository 告警仓库接口
type AlertRepository interface {
	Create(ctx context.Context, alert *model.Alert) error
	Update(ctx context.Context, alert *model.Alert) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.Alert, error)
	GetByResourceAndRule(ctx context.Context, resourceID uuid.UUID, ruleID string) (*model.Alert, error)
	List(ctx context.Context, query *model.AlertQuery, limit, offset int) ([]model.Alert, int64, error)
	ListByResource(ctx context.Context, resourceID uuid.UUID) ([]model.Alert, error)
	ListByStatus(ctx context.Context, status model.AlertStatus) ([]model.Alert, error)
	CountByLevel(ctx context.Context, tenantID uuid.UUID) (map[model.AlertLevel]int64, error)
}

// alertRepository 告警仓库实现
type alertRepository struct {
	db *gorm.DB
}

// NewAlertRepository 创建告警仓库
func NewAlertRepository(db *gorm.DB) AlertRepository {
	return &alertRepository{db: db}
}

// Create 创建告警
func (r *alertRepository) Create(ctx context.Context, alert *model.Alert) error {
	if err := r.db.WithContext(ctx).Create(alert).Error; err != nil {
		logger.Errorf("Failed to create alert: %v", err)
		return err
	}
	return nil
}

// Update 更新告警
func (r *alertRepository) Update(ctx context.Context, alert *model.Alert) error {
	if err := r.db.WithContext(ctx).Save(alert).Error; err != nil {
		logger.Errorf("Failed to update alert: %v", err)
		return err
	}
	return nil
}

// Delete 删除告警
func (r *alertRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&model.Alert{}, id).Error; err != nil {
		logger.Errorf("Failed to delete alert: %v", err)
		return err
	}
	return nil
}

// GetByID 根据ID获取告警
func (r *alertRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.Alert, error) {
	var alert model.Alert
	if err := r.db.WithContext(ctx).
		Preload("Resource").
		First(&alert, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Errorf("Failed to get alert by ID: %v", err)
		return nil, err
	}
	return &alert, nil
}

// GetByResourceAndRule 根据资源和规则获取告警
func (r *alertRepository) GetByResourceAndRule(ctx context.Context, resourceID uuid.UUID, ruleID string) (*model.Alert, error) {
	var alert model.Alert
	if err := r.db.WithContext(ctx).
		Where("resource_id = ? AND rule_id = ? AND status = ?", resourceID, ruleID, model.AlertStatusOpen).
		First(&alert).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Errorf("Failed to get alert by resource and rule: %v", err)
		return nil, err
	}
	return &alert, nil
}

// List 列出告警
func (r *alertRepository) List(ctx context.Context, query *model.AlertQuery, limit, offset int) ([]model.Alert, int64, error) {
	var alerts []model.Alert
	var total int64
	
	db := r.db.WithContext(ctx).Model(&model.Alert{})
	
	// 应用过滤条件
	if query.ResourceID != uuid.Nil {
		db = db.Where("resource_id = ?", query.ResourceID)
	}
	if query.Level != "" {
		db = db.Where("level = ?", query.Level)
	}
	if query.Status != "" {
		db = db.Where("status = ?", query.Status)
	}
	if query.Source != "" {
		db = db.Where("source = ?", query.Source)
	}
	if !query.StartTime.IsZero() {
		db = db.Where("created_at >= ?", query.StartTime)
	}
	if !query.EndTime.IsZero() {
		db = db.Where("created_at <= ?", query.EndTime)
	}
	
	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		logger.Errorf("Failed to count alerts: %v", err)
		return nil, 0, err
	}
	
	// 获取数据
	if err := db.Preload("Resource").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&alerts).Error; err != nil {
		logger.Errorf("Failed to list alerts: %v", err)
		return nil, 0, err
	}
	
	return alerts, total, nil
}

// ListByResource 根据资源列出告警
func (r *alertRepository) ListByResource(ctx context.Context, resourceID uuid.UUID) ([]model.Alert, error) {
	var alerts []model.Alert
	if err := r.db.WithContext(ctx).
		Where("resource_id = ?", resourceID).
		Order("created_at DESC").
		Find(&alerts).Error; err != nil {
		logger.Errorf("Failed to list alerts by resource: %v", err)
		return nil, err
	}
	return alerts, nil
}

// ListByStatus 根据状态列出告警
func (r *alertRepository) ListByStatus(ctx context.Context, status model.AlertStatus) ([]model.Alert, error) {
	var alerts []model.Alert
	if err := r.db.WithContext(ctx).
		Where("status = ?", status).
		Order("created_at DESC").
		Find(&alerts).Error; err != nil {
		logger.Errorf("Failed to list alerts by status: %v", err)
		return nil, err
	}
	return alerts, nil
}

// CountByLevel 根据级别统计告警数量
func (r *alertRepository) CountByLevel(ctx context.Context, tenantID uuid.UUID) (map[model.AlertLevel]int64, error) {
	var results []struct {
		Level model.AlertLevel `json:"level"`
		Count int64            `json:"count"`
	}
	
	if err := r.db.WithContext(ctx).
		Model(&model.Alert{}).
		Select("level, count(*) as count").
		Where("tenant_id = ? AND status = ?", tenantID, model.AlertStatusOpen).
		Group("level").
		Scan(&results).Error; err != nil {
		logger.Errorf("Failed to count alerts by level: %v", err)
		return nil, err
	}
	
	counts := make(map[model.AlertLevel]int64)
	for _, result := range results {
		counts[result.Level] = result.Count
	}
	
	return counts, nil
}
