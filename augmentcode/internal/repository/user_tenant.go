package repository

import (
	"context"

	"cmdb-platform/internal/model"
	"cmdb-platform/pkg/logger"
	
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserTenantRepository 用户租户仓库接口
type UserTenantRepository interface {
	Create(ctx context.Context, userTenant *model.UserTenant) error
	Update(ctx context.Context, userTenant *model.UserTenant) error
	Delete(ctx context.Context, userID, tenantID uuid.UUID) error
	GetByUserAndTenant(ctx context.Context, userID, tenantID uuid.UUID) (*model.UserTenant, error)
	ListByUser(ctx context.Context, userID uuid.UUID) ([]model.UserTenant, error)
	ListByTenant(ctx context.Context, tenantID uuid.UUID) ([]model.UserTenant, error)
	GetDefaultTenant(ctx context.Context, userID uuid.UUID) (*model.UserTenant, error)
}

// userTenantRepository 用户租户仓库实现
type userTenantRepository struct {
	db *gorm.DB
}

// NewUserTenantRepository 创建用户租户仓库
func NewUserTenantRepository(db *gorm.DB) UserTenantRepository {
	return &userTenantRepository{db: db}
}

// Create 创建用户租户关联
func (r *userTenantRepository) Create(ctx context.Context, userTenant *model.UserTenant) error {
	if err := r.db.WithContext(ctx).Create(userTenant).Error; err != nil {
		logger.Errorf("Failed to create user tenant: %v", err)
		return err
	}
	return nil
}

// Update 更新用户租户关联
func (r *userTenantRepository) Update(ctx context.Context, userTenant *model.UserTenant) error {
	if err := r.db.WithContext(ctx).Save(userTenant).Error; err != nil {
		logger.Errorf("Failed to update user tenant: %v", err)
		return err
	}
	return nil
}

// Delete 删除用户租户关联
func (r *userTenantRepository) Delete(ctx context.Context, userID, tenantID uuid.UUID) error {
	if err := r.db.WithContext(ctx).
		Where("user_id = ? AND tenant_id = ?", userID, tenantID).
		Delete(&model.UserTenant{}).Error; err != nil {
		logger.Errorf("Failed to delete user tenant: %v", err)
		return err
	}
	return nil
}

// GetByUserAndTenant 根据用户和租户获取关联
func (r *userTenantRepository) GetByUserAndTenant(ctx context.Context, userID, tenantID uuid.UUID) (*model.UserTenant, error) {
	var userTenant model.UserTenant
	if err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Tenant").
		Where("user_id = ? AND tenant_id = ?", userID, tenantID).
		First(&userTenant).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Errorf("Failed to get user tenant: %v", err)
		return nil, err
	}
	return &userTenant, nil
}

// ListByUser 根据用户列出租户关联
func (r *userTenantRepository) ListByUser(ctx context.Context, userID uuid.UUID) ([]model.UserTenant, error) {
	var userTenants []model.UserTenant
	if err := r.db.WithContext(ctx).
		Preload("Tenant").
		Where("user_id = ?", userID).
		Find(&userTenants).Error; err != nil {
		logger.Errorf("Failed to list user tenants by user: %v", err)
		return nil, err
	}
	return userTenants, nil
}

// ListByTenant 根据租户列出用户关联
func (r *userTenantRepository) ListByTenant(ctx context.Context, tenantID uuid.UUID) ([]model.UserTenant, error) {
	var userTenants []model.UserTenant
	if err := r.db.WithContext(ctx).
		Preload("User").
		Where("tenant_id = ?", tenantID).
		Find(&userTenants).Error; err != nil {
		logger.Errorf("Failed to list user tenants by tenant: %v", err)
		return nil, err
	}
	return userTenants, nil
}

// GetDefaultTenant 获取用户的默认租户
func (r *userTenantRepository) GetDefaultTenant(ctx context.Context, userID uuid.UUID) (*model.UserTenant, error) {
	var userTenant model.UserTenant
	if err := r.db.WithContext(ctx).
		Preload("Tenant").
		Where("user_id = ?", userID).
		Order("created_at ASC").
		First(&userTenant).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Errorf("Failed to get default tenant: %v", err)
		return nil, err
	}
	return &userTenant, nil
}
