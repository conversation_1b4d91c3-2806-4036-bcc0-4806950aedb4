package repository

import (
	"context"
	"time"

	"cmdb-platform/internal/model"
	"cmdb-platform/pkg/logger"
	
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserSessionRepository 用户会话仓库接口
type UserSessionRepository interface {
	Create(ctx context.Context, session *model.UserSession) error
	Update(ctx context.Context, session *model.UserSession) error
	Delete(ctx context.Context, id uuid.UUID) error
	DeleteByToken(ctx context.Context, token string) error
	DeleteByUser(ctx context.Context, userID uuid.UUID) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.UserSession, error)
	GetByToken(ctx context.Context, token string) (*model.UserSession, error)
	ListByUser(ctx context.Context, userID uuid.UUID) ([]model.UserSession, error)
	CleanExpired(ctx context.Context) error
}

// userSessionRepository 用户会话仓库实现
type userSessionRepository struct {
	db *gorm.DB
}

// NewUserSessionRepository 创建用户会话仓库
func NewUserSessionRepository(db *gorm.DB) UserSessionRepository {
	return &userSessionRepository{db: db}
}

// Create 创建用户会话
func (r *userSessionRepository) Create(ctx context.Context, session *model.UserSession) error {
	if err := r.db.WithContext(ctx).Create(session).Error; err != nil {
		logger.Errorf("Failed to create user session: %v", err)
		return err
	}
	return nil
}

// Update 更新用户会话
func (r *userSessionRepository) Update(ctx context.Context, session *model.UserSession) error {
	if err := r.db.WithContext(ctx).Save(session).Error; err != nil {
		logger.Errorf("Failed to update user session: %v", err)
		return err
	}
	return nil
}

// Delete 删除用户会话
func (r *userSessionRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&model.UserSession{}, id).Error; err != nil {
		logger.Errorf("Failed to delete user session: %v", err)
		return err
	}
	return nil
}

// DeleteByToken 根据令牌删除会话
func (r *userSessionRepository) DeleteByToken(ctx context.Context, token string) error {
	if err := r.db.WithContext(ctx).
		Where("token = ?", token).
		Delete(&model.UserSession{}).Error; err != nil {
		logger.Errorf("Failed to delete user session by token: %v", err)
		return err
	}
	return nil
}

// DeleteByUser 删除用户的所有会话
func (r *userSessionRepository) DeleteByUser(ctx context.Context, userID uuid.UUID) error {
	if err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Delete(&model.UserSession{}).Error; err != nil {
		logger.Errorf("Failed to delete user sessions by user: %v", err)
		return err
	}
	return nil
}

// GetByID 根据ID获取用户会话
func (r *userSessionRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.UserSession, error) {
	var session model.UserSession
	if err := r.db.WithContext(ctx).
		Preload("User").
		First(&session, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Errorf("Failed to get user session by ID: %v", err)
		return nil, err
	}
	return &session, nil
}

// GetByToken 根据令牌获取用户会话
func (r *userSessionRepository) GetByToken(ctx context.Context, token string) (*model.UserSession, error) {
	var session model.UserSession
	if err := r.db.WithContext(ctx).
		Preload("User").
		Where("token = ?", token).
		First(&session).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Errorf("Failed to get user session by token: %v", err)
		return nil, err
	}
	return &session, nil
}

// ListByUser 根据用户列出会话
func (r *userSessionRepository) ListByUser(ctx context.Context, userID uuid.UUID) ([]model.UserSession, error) {
	var sessions []model.UserSession
	if err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Find(&sessions).Error; err != nil {
		logger.Errorf("Failed to list user sessions by user: %v", err)
		return nil, err
	}
	return sessions, nil
}

// CleanExpired 清理过期会话
func (r *userSessionRepository) CleanExpired(ctx context.Context) error {
	if err := r.db.WithContext(ctx).
		Where("expires_at < ?", time.Now()).
		Delete(&model.UserSession{}).Error; err != nil {
		logger.Errorf("Failed to clean expired sessions: %v", err)
		return err
	}
	return nil
}
