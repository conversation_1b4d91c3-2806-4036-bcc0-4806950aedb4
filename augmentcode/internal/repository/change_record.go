package repository

import (
	"context"

	"cmdb-platform/internal/model"
	"cmdb-platform/pkg/logger"
	
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ChangeRecordRepository 变更记录仓库接口
type ChangeRecordRepository interface {
	Create(ctx context.Context, record *model.ChangeRecord) error
	BatchCreate(ctx context.Context, records []model.ChangeRecord) error
	Update(ctx context.Context, record *model.ChangeRecord) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.ChangeRecord, error)
	ListByResource(ctx context.Context, resourceID uuid.UUID, limit, offset int) ([]model.ChangeRecord, int64, error)
	ListByTenant(ctx context.Context, tenantID uuid.UUID, limit, offset int) ([]model.ChangeRecord, int64, error)
	ListByType(ctx context.Context, changeType model.ChangeType, limit, offset int) ([]model.ChangeRecord, int64, error)
}

// changeRecordRepository 变更记录仓库实现
type changeRecordRepository struct {
	db *gorm.DB
}

// NewChangeRecordRepository 创建变更记录仓库
func NewChangeRecordRepository(db *gorm.DB) ChangeRecordRepository {
	return &changeRecordRepository{db: db}
}

// Create 创建变更记录
func (r *changeRecordRepository) Create(ctx context.Context, record *model.ChangeRecord) error {
	if err := r.db.WithContext(ctx).Create(record).Error; err != nil {
		logger.Errorf("Failed to create change record: %v", err)
		return err
	}
	return nil
}

// BatchCreate 批量创建变更记录
func (r *changeRecordRepository) BatchCreate(ctx context.Context, records []model.ChangeRecord) error {
	if len(records) == 0 {
		return nil
	}
	
	// 使用事务批量插入
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		batchSize := 100
		for i := 0; i < len(records); i += batchSize {
			end := i + batchSize
			if end > len(records) {
				end = len(records)
			}
			
			if err := tx.Create(records[i:end]).Error; err != nil {
				logger.Errorf("Failed to batch create change records: %v", err)
				return err
			}
		}
		return nil
	})
}

// Update 更新变更记录
func (r *changeRecordRepository) Update(ctx context.Context, record *model.ChangeRecord) error {
	if err := r.db.WithContext(ctx).Save(record).Error; err != nil {
		logger.Errorf("Failed to update change record: %v", err)
		return err
	}
	return nil
}

// Delete 删除变更记录
func (r *changeRecordRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&model.ChangeRecord{}, id).Error; err != nil {
		logger.Errorf("Failed to delete change record: %v", err)
		return err
	}
	return nil
}

// GetByID 根据ID获取变更记录
func (r *changeRecordRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.ChangeRecord, error) {
	var record model.ChangeRecord
	if err := r.db.WithContext(ctx).
		Preload("Resource").
		First(&record, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Errorf("Failed to get change record by ID: %v", err)
		return nil, err
	}
	return &record, nil
}

// ListByResource 根据资源列出变更记录
func (r *changeRecordRepository) ListByResource(ctx context.Context, resourceID uuid.UUID, limit, offset int) ([]model.ChangeRecord, int64, error) {
	var records []model.ChangeRecord
	var total int64
	
	db := r.db.WithContext(ctx).Model(&model.ChangeRecord{}).Where("resource_id = ?", resourceID)
	
	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		logger.Errorf("Failed to count change records: %v", err)
		return nil, 0, err
	}
	
	// 获取数据
	if err := db.Preload("Resource").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&records).Error; err != nil {
		logger.Errorf("Failed to list change records by resource: %v", err)
		return nil, 0, err
	}
	
	return records, total, nil
}

// ListByTenant 根据租户列出变更记录
func (r *changeRecordRepository) ListByTenant(ctx context.Context, tenantID uuid.UUID, limit, offset int) ([]model.ChangeRecord, int64, error) {
	var records []model.ChangeRecord
	var total int64
	
	db := r.db.WithContext(ctx).Model(&model.ChangeRecord{}).Where("tenant_id = ?", tenantID)
	
	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		logger.Errorf("Failed to count change records: %v", err)
		return nil, 0, err
	}
	
	// 获取数据
	if err := db.Preload("Resource").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&records).Error; err != nil {
		logger.Errorf("Failed to list change records by tenant: %v", err)
		return nil, 0, err
	}
	
	return records, total, nil
}

// ListByType 根据变更类型列出变更记录
func (r *changeRecordRepository) ListByType(ctx context.Context, changeType model.ChangeType, limit, offset int) ([]model.ChangeRecord, int64, error) {
	var records []model.ChangeRecord
	var total int64
	
	db := r.db.WithContext(ctx).Model(&model.ChangeRecord{}).Where("type = ?", changeType)
	
	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		logger.Errorf("Failed to count change records: %v", err)
		return nil, 0, err
	}
	
	// 获取数据
	if err := db.Preload("Resource").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&records).Error; err != nil {
		logger.Errorf("Failed to list change records by type: %v", err)
		return nil, 0, err
	}
	
	return records, total, nil
}
