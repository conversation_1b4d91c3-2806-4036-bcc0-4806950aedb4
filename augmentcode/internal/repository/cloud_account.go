package repository

import (
	"context"

	"cmdb-platform/internal/model"
	"cmdb-platform/pkg/logger"
	
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// CloudAccountRepository 云账号仓库接口
type CloudAccountRepository interface {
	Create(ctx context.Context, account *model.CloudAccount) error
	Update(ctx context.Context, account *model.CloudAccount) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.CloudAccount, error)
	List(ctx context.Context, tenantID uuid.UUID) ([]model.CloudAccount, error)
	ListByProvider(ctx context.Context, provider model.CloudProvider) ([]model.CloudAccount, error)
}

// cloudAccountRepository 云账号仓库实现
type cloudAccountRepository struct {
	db *gorm.DB
}

// NewCloudAccountRepository 创建云账号仓库
func NewCloudAccountRepository(db *gorm.DB) CloudAccountRepository {
	return &cloudAccountRepository{db: db}
}

// Create 创建云账号
func (r *cloudAccountRepository) Create(ctx context.Context, account *model.CloudAccount) error {
	if err := r.db.WithContext(ctx).Create(account).Error; err != nil {
		logger.Errorf("Failed to create cloud account: %v", err)
		return err
	}
	return nil
}

// Update 更新云账号
func (r *cloudAccountRepository) Update(ctx context.Context, account *model.CloudAccount) error {
	if err := r.db.WithContext(ctx).Save(account).Error; err != nil {
		logger.Errorf("Failed to update cloud account: %v", err)
		return err
	}
	return nil
}

// Delete 删除云账号
func (r *cloudAccountRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&model.CloudAccount{}, id).Error; err != nil {
		logger.Errorf("Failed to delete cloud account: %v", err)
		return err
	}
	return nil
}

// GetByID 根据ID获取云账号
func (r *cloudAccountRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.CloudAccount, error) {
	var account model.CloudAccount
	if err := r.db.WithContext(ctx).First(&account, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Errorf("Failed to get cloud account by ID: %v", err)
		return nil, err
	}
	return &account, nil
}

// List 列出云账号
func (r *cloudAccountRepository) List(ctx context.Context, tenantID uuid.UUID) ([]model.CloudAccount, error) {
	var accounts []model.CloudAccount
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ?", tenantID).
		Find(&accounts).Error; err != nil {
		logger.Errorf("Failed to list cloud accounts: %v", err)
		return nil, err
	}
	return accounts, nil
}

// ListByProvider 根据云服务商列出云账号
func (r *cloudAccountRepository) ListByProvider(ctx context.Context, provider model.CloudProvider) ([]model.CloudAccount, error) {
	var accounts []model.CloudAccount
	if err := r.db.WithContext(ctx).
		Where("provider = ?", provider).
		Find(&accounts).Error; err != nil {
		logger.Errorf("Failed to list cloud accounts by provider: %v", err)
		return nil, err
	}
	return accounts, nil
}
