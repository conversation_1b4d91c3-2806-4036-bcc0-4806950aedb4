package repository

import (
	"context"
	"fmt"

	"cmdb-platform/internal/model"
	"cmdb-platform/pkg/logger"
	
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ResourceRepository 资源仓库接口
type ResourceRepository interface {
	Create(ctx context.Context, resource *model.Resource) error
	Update(ctx context.Context, resource *model.Resource) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.Resource, error)
	GetByCloudID(ctx context.Context, cloudAccountID uuid.UUID, cloudID string) (*model.Resource, error)
	List(ctx context.Context, query *model.ResourceQuery) ([]model.Resource, int64, error)
	ListByCloudAccount(ctx context.Context, cloudAccountID uuid.UUID) ([]model.Resource, error)
	ListByType(ctx context.Context, resourceType model.ResourceType) ([]model.Resource, error)
	BatchCreate(ctx context.Context, resources []model.Resource) error
	BatchUpdate(ctx context.Context, resources []model.Resource) error
	GetStats(ctx context.Context, tenantID uuid.UUID) (*model.StatsResponse, error)
}

// resourceRepository 资源仓库实现
type resourceRepository struct {
	db *gorm.DB
}

// NewResourceRepository 创建资源仓库
func NewResourceRepository(db *gorm.DB) ResourceRepository {
	return &resourceRepository{db: db}
}

// Create 创建资源
func (r *resourceRepository) Create(ctx context.Context, resource *model.Resource) error {
	if err := r.db.WithContext(ctx).Create(resource).Error; err != nil {
		logger.Errorf("Failed to create resource: %v", err)
		return err
	}
	return nil
}

// Update 更新资源
func (r *resourceRepository) Update(ctx context.Context, resource *model.Resource) error {
	if err := r.db.WithContext(ctx).Save(resource).Error; err != nil {
		logger.Errorf("Failed to update resource: %v", err)
		return err
	}
	return nil
}

// Delete 删除资源
func (r *resourceRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&model.Resource{}, id).Error; err != nil {
		logger.Errorf("Failed to delete resource: %v", err)
		return err
	}
	return nil
}

// GetByID 根据ID获取资源
func (r *resourceRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.Resource, error) {
	var resource model.Resource
	if err := r.db.WithContext(ctx).
		Preload("CloudAccount").
		Preload("ConfigItems").
		First(&resource, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Errorf("Failed to get resource by ID: %v", err)
		return nil, err
	}
	return &resource, nil
}

// GetByCloudID 根据云资源ID获取资源
func (r *resourceRepository) GetByCloudID(ctx context.Context, cloudAccountID uuid.UUID, cloudID string) (*model.Resource, error) {
	var resource model.Resource
	if err := r.db.WithContext(ctx).
		Where("cloud_account_id = ? AND cloud_id = ?", cloudAccountID, cloudID).
		First(&resource).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Errorf("Failed to get resource by cloud ID: %v", err)
		return nil, err
	}
	return &resource, nil
}

// List 列出资源
func (r *resourceRepository) List(ctx context.Context, query *model.ResourceQuery) ([]model.Resource, int64, error) {
	var resources []model.Resource
	var total int64
	
	db := r.db.WithContext(ctx).Model(&model.Resource{})
	
	// 应用过滤条件
	if query.Provider != "" {
		db = db.Where("provider = ?", query.Provider)
	}
	if query.Type != "" {
		db = db.Where("type = ?", query.Type)
	}
	if query.Status != "" {
		db = db.Where("status = ?", query.Status)
	}
	if query.Region != "" {
		db = db.Where("region = ?", query.Region)
	}
	if query.Zone != "" {
		db = db.Where("zone = ?", query.Zone)
	}
	if query.CloudAccountID != uuid.Nil {
		db = db.Where("cloud_account_id = ?", query.CloudAccountID)
	}
	if query.Name != "" {
		db = db.Where("name ILIKE ?", "%"+query.Name+"%")
	}
	
	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		logger.Errorf("Failed to count resources: %v", err)
		return nil, 0, err
	}
	
	// 应用分页和排序
	offset := (query.Page - 1) * query.PageSize
	db = db.Offset(offset).Limit(query.PageSize)
	
	if query.SortBy != "" {
		order := fmt.Sprintf("%s %s", query.SortBy, query.SortOrder)
		db = db.Order(order)
	}
	
	// 预加载关联数据
	db = db.Preload("CloudAccount")
	
	if err := db.Find(&resources).Error; err != nil {
		logger.Errorf("Failed to list resources: %v", err)
		return nil, 0, err
	}
	
	return resources, total, nil
}

// ListByCloudAccount 根据云账号列出资源
func (r *resourceRepository) ListByCloudAccount(ctx context.Context, cloudAccountID uuid.UUID) ([]model.Resource, error) {
	var resources []model.Resource
	if err := r.db.WithContext(ctx).
		Where("cloud_account_id = ?", cloudAccountID).
		Find(&resources).Error; err != nil {
		logger.Errorf("Failed to list resources by cloud account: %v", err)
		return nil, err
	}
	return resources, nil
}

// ListByType 根据资源类型列出资源
func (r *resourceRepository) ListByType(ctx context.Context, resourceType model.ResourceType) ([]model.Resource, error) {
	var resources []model.Resource
	if err := r.db.WithContext(ctx).
		Where("type = ?", resourceType).
		Find(&resources).Error; err != nil {
		logger.Errorf("Failed to list resources by type: %v", err)
		return nil, err
	}
	return resources, nil
}

// BatchCreate 批量创建资源
func (r *resourceRepository) BatchCreate(ctx context.Context, resources []model.Resource) error {
	if len(resources) == 0 {
		return nil
	}
	
	// 使用事务批量插入
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		batchSize := 100
		for i := 0; i < len(resources); i += batchSize {
			end := i + batchSize
			if end > len(resources) {
				end = len(resources)
			}
			
			if err := tx.Create(resources[i:end]).Error; err != nil {
				logger.Errorf("Failed to batch create resources: %v", err)
				return err
			}
		}
		return nil
	})
}

// BatchUpdate 批量更新资源
func (r *resourceRepository) BatchUpdate(ctx context.Context, resources []model.Resource) error {
	if len(resources) == 0 {
		return nil
	}
	
	// 使用事务批量更新
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, resource := range resources {
			if err := tx.Save(&resource).Error; err != nil {
				logger.Errorf("Failed to batch update resource %s: %v", resource.ID, err)
				return err
			}
		}
		return nil
	})
}

// GetStats 获取资源统计信息
func (r *resourceRepository) GetStats(ctx context.Context, tenantID uuid.UUID) (*model.StatsResponse, error) {
	stats := &model.StatsResponse{
		ResourcesByType:   make(map[model.ResourceType]int64),
		ResourcesByCloud:  make(map[model.CloudProvider]int64),
		ResourcesByStatus: make(map[model.ResourceStatus]int64),
	}
	
	// 总资源数
	if err := r.db.WithContext(ctx).
		Model(&model.Resource{}).
		Where("tenant_id = ?", tenantID).
		Count(&stats.TotalResources).Error; err != nil {
		return nil, err
	}
	
	// 按类型统计
	var typeStats []struct {
		Type  model.ResourceType `json:"type"`
		Count int64              `json:"count"`
	}
	if err := r.db.WithContext(ctx).
		Model(&model.Resource{}).
		Select("type, count(*) as count").
		Where("tenant_id = ?", tenantID).
		Group("type").
		Scan(&typeStats).Error; err != nil {
		return nil, err
	}
	for _, stat := range typeStats {
		stats.ResourcesByType[stat.Type] = stat.Count
	}
	
	// 按云服务商统计
	var cloudStats []struct {
		Provider model.CloudProvider `json:"provider"`
		Count    int64               `json:"count"`
	}
	if err := r.db.WithContext(ctx).
		Model(&model.Resource{}).
		Select("provider, count(*) as count").
		Where("tenant_id = ?", tenantID).
		Group("provider").
		Scan(&cloudStats).Error; err != nil {
		return nil, err
	}
	for _, stat := range cloudStats {
		stats.ResourcesByCloud[stat.Provider] = stat.Count
	}
	
	// 按状态统计
	var statusStats []struct {
		Status model.ResourceStatus `json:"status"`
		Count  int64                `json:"count"`
	}
	if err := r.db.WithContext(ctx).
		Model(&model.Resource{}).
		Select("status, count(*) as count").
		Where("tenant_id = ?", tenantID).
		Group("status").
		Scan(&statusStats).Error; err != nil {
		return nil, err
	}
	for _, stat := range statusStats {
		stats.ResourcesByStatus[stat.Status] = stat.Count
	}
	
	// 告警统计
	if err := r.db.WithContext(ctx).
		Model(&model.Alert{}).
		Where("tenant_id = ?", tenantID).
		Count(&stats.AlertsCount).Error; err != nil {
		return nil, err
	}
	
	if err := r.db.WithContext(ctx).
		Model(&model.Alert{}).
		Where("tenant_id = ? AND status = ?", tenantID, model.AlertStatusOpen).
		Count(&stats.ActiveAlerts).Error; err != nil {
		return nil, err
	}
	
	return stats, nil
}
