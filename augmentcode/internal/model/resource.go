package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
)

// Resource 资源模型
type Resource struct {
	TenantModel
	CloudAccountID uuid.UUID      `json:"cloud_account_id" gorm:"type:uuid;not null"`
	Provider       CloudProvider  `json:"provider" gorm:"not null"`
	Type           ResourceType   `json:"type" gorm:"not null"`
	Name           string         `json:"name" gorm:"not null;size:255"`
	CloudID        string         `json:"cloud_id" gorm:"not null;size:255;index"`
	Region         string         `json:"region" gorm:"not null;size:50"`
	Zone           string         `json:"zone" gorm:"size:50"`
	Status         ResourceStatus `json:"status" gorm:"not null"`
	Tags           datatypes.JSON `json:"tags" gorm:"type:jsonb"`
	Properties     datatypes.JSON `json:"properties" gorm:"type:jsonb"`
	Metadata       datatypes.JSON `json:"metadata" gorm:"type:jsonb"`
	LastSyncAt     *time.Time     `json:"last_sync_at"`
	
	// 关联关系
	CloudAccount     CloudAccount       `json:"cloud_account,omitempty" gorm:"foreignKey:CloudAccountID"`
	ConfigItems      []ConfigItem       `json:"config_items,omitempty" gorm:"foreignKey:ResourceID"`
	SourceRelations  []ResourceRelation `json:"source_relations,omitempty" gorm:"foreignKey:SourceResourceID"`
	TargetRelations  []ResourceRelation `json:"target_relations,omitempty" gorm:"foreignKey:TargetResourceID"`
	ChangeRecords    []ChangeRecord     `json:"change_records,omitempty" gorm:"foreignKey:ResourceID"`
	Alerts           []Alert            `json:"alerts,omitempty" gorm:"foreignKey:ResourceID"`
}

// ConfigItem 配置项模型
type ConfigItem struct {
	TenantModel
	ResourceID  uuid.UUID      `json:"resource_id" gorm:"type:uuid;not null"`
	Key         string         `json:"key" gorm:"not null;size:255"`
	Value       string         `json:"value" gorm:"type:text"`
	Type        string         `json:"type" gorm:"not null;size:50"`
	Description string         `json:"description" gorm:"size:500"`
	IsSecret    bool           `json:"is_secret" gorm:"default:false"`
	Tags        datatypes.JSON `json:"tags" gorm:"type:jsonb"`
	
	// 关联关系
	Resource Resource `json:"resource,omitempty" gorm:"foreignKey:ResourceID"`
}

// ResourceRelation 资源关系模型
type ResourceRelation struct {
	TenantModel
	SourceResourceID uuid.UUID    `json:"source_resource_id" gorm:"type:uuid;not null"`
	TargetResourceID uuid.UUID    `json:"target_resource_id" gorm:"type:uuid;not null"`
	Type             RelationType `json:"type" gorm:"not null"`
	Description      string       `json:"description" gorm:"size:500"`
	Properties       datatypes.JSON `json:"properties" gorm:"type:jsonb"`
	
	// 关联关系
	SourceResource Resource `json:"source_resource,omitempty" gorm:"foreignKey:SourceResourceID"`
	TargetResource Resource `json:"target_resource,omitempty" gorm:"foreignKey:TargetResourceID"`
}

// ChangeRecord 变更记录模型
type ChangeRecord struct {
	TenantModel
	ResourceID  uuid.UUID  `json:"resource_id" gorm:"type:uuid;not null"`
	Type        ChangeType `json:"type" gorm:"not null"`
	Field       string     `json:"field" gorm:"size:255"`
	OldValue    string     `json:"old_value" gorm:"type:text"`
	NewValue    string     `json:"new_value" gorm:"type:text"`
	Description string     `json:"description" gorm:"size:1000"`
	Source      string     `json:"source" gorm:"not null;size:100"` // manual, sync, api
	
	// 关联关系
	Resource Resource `json:"resource,omitempty" gorm:"foreignKey:ResourceID"`
}

// Alert 告警模型
type Alert struct {
	TenantModel
	ResourceID  uuid.UUID   `json:"resource_id" gorm:"type:uuid"`
	Title       string      `json:"title" gorm:"not null;size:255"`
	Description string      `json:"description" gorm:"type:text"`
	Level       AlertLevel  `json:"level" gorm:"not null"`
	Status      AlertStatus `json:"status" gorm:"not null;default:'open'"`
	Source      string      `json:"source" gorm:"not null;size:100"`
	RuleID      string      `json:"rule_id" gorm:"size:255"`
	Tags        datatypes.JSON `json:"tags" gorm:"type:jsonb"`
	ResolvedAt  *time.Time  `json:"resolved_at"`
	
	// 关联关系
	Resource Resource `json:"resource,omitempty" gorm:"foreignKey:ResourceID"`
}

// SyncTask 同步任务模型
type SyncTask struct {
	TenantModel
	CloudAccountID uuid.UUID  `json:"cloud_account_id" gorm:"type:uuid;not null"`
	Type           string     `json:"type" gorm:"not null;size:100"` // full, incremental
	Status         SyncStatus `json:"status" gorm:"not null"`
	StartedAt      *time.Time `json:"started_at"`
	CompletedAt    *time.Time `json:"completed_at"`
	TotalCount     int        `json:"total_count" gorm:"default:0"`
	SuccessCount   int        `json:"success_count" gorm:"default:0"`
	FailureCount   int        `json:"failure_count" gorm:"default:0"`
	ErrorMessage   string     `json:"error_message" gorm:"type:text"`
	
	// 关联关系
	CloudAccount CloudAccount `json:"cloud_account,omitempty" gorm:"foreignKey:CloudAccountID"`
}

// ResourceTemplate 资源模板模型
type ResourceTemplate struct {
	TenantModel
	Name        string         `json:"name" gorm:"not null;size:255"`
	Type        ResourceType   `json:"type" gorm:"not null"`
	Provider    CloudProvider  `json:"provider" gorm:"not null"`
	Description string         `json:"description" gorm:"size:1000"`
	Template    datatypes.JSON `json:"template" gorm:"type:jsonb"`
	Variables   datatypes.JSON `json:"variables" gorm:"type:jsonb"`
	Tags        datatypes.JSON `json:"tags" gorm:"type:jsonb"`
}

// ResourceGroup 资源组模型
type ResourceGroup struct {
	TenantModel
	Name        string         `json:"name" gorm:"not null;size:255"`
	Description string         `json:"description" gorm:"size:1000"`
	Tags        datatypes.JSON `json:"tags" gorm:"type:jsonb"`
	
	// 关联关系
	Resources []Resource `json:"resources,omitempty" gorm:"many2many:resource_group_resources;"`
}

// TableName 指定表名
func (Resource) TableName() string {
	return "resources"
}

func (ConfigItem) TableName() string {
	return "config_items"
}

func (ResourceRelation) TableName() string {
	return "resource_relations"
}

func (ChangeRecord) TableName() string {
	return "change_records"
}

func (Alert) TableName() string {
	return "alerts"
}

func (SyncTask) TableName() string {
	return "sync_tasks"
}

func (ResourceTemplate) TableName() string {
	return "resource_templates"
}

func (ResourceGroup) TableName() string {
	return "resource_groups"
}
