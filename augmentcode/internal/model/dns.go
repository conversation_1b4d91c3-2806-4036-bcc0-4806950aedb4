package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
)

// DNSRecordType DNS记录类型枚举
type DNSRecordType string

const (
	DNSRecordTypeA     DNSRecordType = "A"
	DNSRecordTypeAAAA  DNSRecordType = "AAAA"
	DNSRecordTypeCNAME DNSRecordType = "CNAME"
	DNSRecordTypeMX    DNSRecordType = "MX"
	DNSRecordTypeTXT   DNSRecordType = "TXT"
	DNSRecordTypeNS    DNSRecordType = "NS"
	DNSRecordTypeSOA   DNSRecordType = "SOA"
	DNSRecordTypeSRV   DNSRecordType = "SRV"
	DNSRecordTypePTR   DNSRecordType = "PTR"
	DNSRecordTypeCAA   DNSRecordType = "CAA"
)

// DNSZoneStatus DNS域名状态枚举
type DNSZoneStatus string

const (
	DNSZoneStatusActive   DNSZoneStatus = "active"
	DNSZoneStatusInactive DNSZoneStatus = "inactive"
	DNSZoneStatusPending  DNSZoneStatus = "pending"
	DNSZoneStatusError    DNSZoneStatus = "error"
)

// DNSRecordStatus DNS记录状态枚举
type DNSRecordStatus string

const (
	DNSRecordStatusActive   DNSRecordStatus = "active"
	DNSRecordStatusInactive DNSRecordStatus = "inactive"
	DNSRecordStatusPending  DNSRecordStatus = "pending"
	DNSRecordStatusError    DNSRecordStatus = "error"
)

// DNSZone DNS域名模型
type DNSZone struct {
	TenantModel
	CloudAccountID uuid.UUID     `json:"cloud_account_id" gorm:"type:uuid;not null;index"`
	Provider       CloudProvider `json:"provider" gorm:"not null;index"`
	CloudZoneID    string        `json:"cloud_zone_id" gorm:"not null;size:255;index"` // 云服务商的域名ID
	DomainName     string        `json:"domain_name" gorm:"not null;size:255;index"`   // 域名
	Status         DNSZoneStatus `json:"status" gorm:"not null;default:'active'"`
	TTL            int           `json:"ttl" gorm:"default:600"`                       // 默认TTL
	Description    string        `json:"description" gorm:"size:500"`
	Tags           datatypes.JSON `json:"tags" gorm:"type:jsonb"`                      // 标签
	Metadata       datatypes.JSON `json:"metadata" gorm:"type:jsonb"`                  // 元数据
	LastSyncAt     *time.Time    `json:"last_sync_at"`                                // 最后同步时间
	
	// 关联关系
	CloudAccount *CloudAccount `json:"cloud_account,omitempty" gorm:"foreignKey:CloudAccountID"`
	Records      []DNSRecord   `json:"records,omitempty" gorm:"foreignKey:ZoneID"`
}

// DNSRecord DNS记录模型
type DNSRecord struct {
	TenantModel
	ZoneID        uuid.UUID       `json:"zone_id" gorm:"type:uuid;not null;index"`
	CloudRecordID string          `json:"cloud_record_id" gorm:"not null;size:255;index"` // 云服务商的记录ID
	Name          string          `json:"name" gorm:"not null;size:255;index"`            // 记录名称
	Type          DNSRecordType   `json:"type" gorm:"not null;index"`                     // 记录类型
	Value         string          `json:"value" gorm:"not null;size:1000"`                // 记录值
	TTL           int             `json:"ttl" gorm:"default:600"`                         // TTL
	Priority      *int            `json:"priority,omitempty"`                             // 优先级(MX记录使用)
	Weight        *int            `json:"weight,omitempty"`                               // 权重(SRV记录使用)
	Port          *int            `json:"port,omitempty"`                                 // 端口(SRV记录使用)
	Status        DNSRecordStatus `json:"status" gorm:"not null;default:'active'"`
	Description   string          `json:"description" gorm:"size:500"`
	Tags          datatypes.JSON  `json:"tags" gorm:"type:jsonb"`     // 标签
	Metadata      datatypes.JSON  `json:"metadata" gorm:"type:jsonb"` // 元数据
	LastSyncAt    *time.Time      `json:"last_sync_at"`               // 最后同步时间
	
	// 关联关系
	Zone *DNSZone `json:"zone,omitempty" gorm:"foreignKey:ZoneID"`
}

// DNSOperationLog DNS操作日志模型
type DNSOperationLog struct {
	TenantModel
	ZoneID      *uuid.UUID    `json:"zone_id,omitempty" gorm:"type:uuid;index"`
	RecordID    *uuid.UUID    `json:"record_id,omitempty" gorm:"type:uuid;index"`
	Operation   string        `json:"operation" gorm:"not null;size:50;index"`     // 操作类型: create, update, delete
	Target      string        `json:"target" gorm:"not null;size:50"`              // 操作目标: zone, record
	TargetName  string        `json:"target_name" gorm:"not null;size:255;index"`  // 目标名称
	OldValue    datatypes.JSON `json:"old_value,omitempty" gorm:"type:jsonb"`       // 操作前的值
	NewValue    datatypes.JSON `json:"new_value,omitempty" gorm:"type:jsonb"`       // 操作后的值
	Status      string        `json:"status" gorm:"not null;size:20;index"`        // 操作状态: success, failed, pending
	ErrorMsg    string        `json:"error_msg,omitempty" gorm:"size:1000"`        // 错误信息
	UserAgent   string        `json:"user_agent,omitempty" gorm:"size:500"`        // 用户代理
	ClientIP    string        `json:"client_ip,omitempty" gorm:"size:45"`          // 客户端IP
	Duration    int           `json:"duration,omitempty"`                          // 操作耗时(毫秒)
	
	// 关联关系
	Zone   *DNSZone   `json:"zone,omitempty" gorm:"foreignKey:ZoneID"`
	Record *DNSRecord `json:"record,omitempty" gorm:"foreignKey:RecordID"`
}

// DNSHealthCheck DNS健康检查模型
type DNSHealthCheck struct {
	TenantModel
	ZoneID      uuid.UUID `json:"zone_id" gorm:"type:uuid;not null;index"`
	RecordID    *uuid.UUID `json:"record_id,omitempty" gorm:"type:uuid;index"`
	CheckType   string    `json:"check_type" gorm:"not null;size:50"`         // 检查类型: resolution, response_time
	Target      string    `json:"target" gorm:"not null;size:255"`            // 检查目标
	Interval    int       `json:"interval" gorm:"not null;default:300"`       // 检查间隔(秒)
	Timeout     int       `json:"timeout" gorm:"not null;default:10"`         // 超时时间(秒)
	Enabled     bool      `json:"enabled" gorm:"not null;default:true"`       // 是否启用
	Status      string    `json:"status" gorm:"not null;size:20;index"`       // 检查状态: healthy, unhealthy, unknown
	LastCheck   *time.Time `json:"last_check,omitempty"`                      // 最后检查时间
	LastError   string    `json:"last_error,omitempty" gorm:"size:1000"`      // 最后错误信息
	
	// 关联关系
	Zone   *DNSZone   `json:"zone,omitempty" gorm:"foreignKey:ZoneID"`
	Record *DNSRecord `json:"record,omitempty" gorm:"foreignKey:RecordID"`
}

// DNSStatistics DNS统计模型
type DNSStatistics struct {
	TenantModel
	ZoneID       uuid.UUID `json:"zone_id" gorm:"type:uuid;not null;index"`
	Date         time.Time `json:"date" gorm:"not null;index"`                    // 统计日期
	QueryCount   int64     `json:"query_count" gorm:"not null;default:0"`         // 查询次数
	ResponseTime float64   `json:"response_time" gorm:"not null;default:0"`       // 平均响应时间(毫秒)
	ErrorCount   int64     `json:"error_count" gorm:"not null;default:0"`         // 错误次数
	ErrorRate    float64   `json:"error_rate" gorm:"not null;default:0"`          // 错误率
	Metadata     datatypes.JSON `json:"metadata,omitempty" gorm:"type:jsonb"`     // 详细统计数据
	
	// 关联关系
	Zone *DNSZone `json:"zone,omitempty" gorm:"foreignKey:ZoneID"`
}

// TableName 设置表名
func (DNSZone) TableName() string {
	return "dns_zones"
}

func (DNSRecord) TableName() string {
	return "dns_records"
}

func (DNSOperationLog) TableName() string {
	return "dns_operation_logs"
}

func (DNSHealthCheck) TableName() string {
	return "dns_health_checks"
}

func (DNSStatistics) TableName() string {
	return "dns_statistics"
}
