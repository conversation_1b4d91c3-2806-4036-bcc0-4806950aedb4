package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BaseModel 基础模型
type BaseModel struct {
	ID        uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
	CreatedBy uuid.UUID      `json:"created_by,omitempty" gorm:"type:uuid"`
	UpdatedBy uuid.UUID      `json:"updated_by,omitempty" gorm:"type:uuid"`
}

// TenantModel 多租户基础模型
type TenantModel struct {
	BaseModel
	TenantID uuid.UUID `json:"tenant_id" gorm:"type:uuid;not null;index"`
}

// CloudProvider 云服务商枚举
type CloudProvider string

const (
	CloudProviderAliyun  CloudProvider = "aliyun"
	CloudProviderTencent CloudProvider = "tencent"
	CloudProviderAWS     CloudProvider = "aws"
)

// CloudAccountStatus 云账户状态枚举
type CloudAccountStatus string

const (
	CloudAccountStatusActive   CloudAccountStatus = "active"
	CloudAccountStatusInactive CloudAccountStatus = "inactive"
	CloudAccountStatusError    CloudAccountStatus = "error"
	CloudAccountStatusPending  CloudAccountStatus = "pending"
)

// ResourceStatus 资源状态枚举
type ResourceStatus string

const (
	ResourceStatusRunning  ResourceStatus = "running"
	ResourceStatusStopped  ResourceStatus = "stopped"
	ResourceStatusStarting ResourceStatus = "starting"
	ResourceStatusStopping ResourceStatus = "stopping"
	ResourceStatusDeleted  ResourceStatus = "deleted"
	ResourceStatusUnknown  ResourceStatus = "unknown"
)

// ResourceType 资源类型枚举
type ResourceType string

const (
	// 计算资源
	ResourceTypeECS ResourceType = "ecs" // 云服务器
	ResourceTypeECI ResourceType = "eci" // 弹性容器实例

	// 网络资源
	ResourceTypeVPC    ResourceType = "vpc"    // 专有网络
	ResourceTypeSubnet ResourceType = "subnet" // 子网
	ResourceTypeSLB    ResourceType = "slb"    // 负载均衡
	ResourceTypeEIP    ResourceType = "eip"    // 弹性公网IP

	// 存储资源
	ResourceTypeEBS ResourceType = "ebs" // 云盘
	ResourceTypeOSS ResourceType = "oss" // 对象存储
	ResourceTypeNAS ResourceType = "nas" // 文件存储

	// 数据库资源
	ResourceTypeRDS   ResourceType = "rds"   // 关系型数据库
	ResourceTypeRedis ResourceType = "redis" // 缓存数据库
	ResourceTypeMongo ResourceType = "mongo" // 文档数据库

	// 安全资源
	ResourceTypeSG  ResourceType = "sg"  // 安全组
	ResourceTypeWAF ResourceType = "waf" // Web应用防火墙

	// 监控资源
	ResourceTypeCMS ResourceType = "cms" // 云监控
	ResourceTypeSLS ResourceType = "sls" // 日志服务

	// DNS资源
	ResourceTypeDNSZone   ResourceType = "dns_zone"   // DNS域名
	ResourceTypeDNSRecord ResourceType = "dns_record" // DNS记录
)

// ChangeType 变更类型枚举
type ChangeType string

const (
	ChangeTypeCreate ChangeType = "create"
	ChangeTypeUpdate ChangeType = "update"
	ChangeTypeDelete ChangeType = "delete"
)

// AlertLevel 告警级别枚举
type AlertLevel string

const (
	AlertLevelInfo     AlertLevel = "info"
	AlertLevelWarning  AlertLevel = "warning"
	AlertLevelCritical AlertLevel = "critical"
)

// AlertStatus 告警状态枚举
type AlertStatus string

const (
	AlertStatusOpen     AlertStatus = "open"
	AlertStatusResolved AlertStatus = "resolved"
	AlertStatusIgnored  AlertStatus = "ignored"
)

// RelationType 关系类型枚举
type RelationType string

const (
	RelationTypeContains   RelationType = "contains"   // 包含关系
	RelationTypeConnects   RelationType = "connects"   // 连接关系
	RelationTypeDepends    RelationType = "depends"    // 依赖关系
	RelationTypeAttaches   RelationType = "attaches"   // 挂载关系
	RelationTypeAssociates RelationType = "associates" // 关联关系
)

// SyncStatus 同步状态枚举
type SyncStatus string

const (
	SyncStatusPending   SyncStatus = "pending"
	SyncStatusRunning   SyncStatus = "running"
	SyncStatusCompleted SyncStatus = "completed"
	SyncStatusFailed    SyncStatus = "failed"
)

// UserRole 用户角色枚举
type UserRole string

const (
	UserRoleAdmin    UserRole = "admin"
	UserRoleOperator UserRole = "operator"
	UserRoleViewer   UserRole = "viewer"
)

// UserStatus 用户状态枚举
type UserStatus string

const (
	UserStatusActive   UserStatus = "active"
	UserStatusInactive UserStatus = "inactive"
	UserStatusLocked   UserStatus = "locked"
)
