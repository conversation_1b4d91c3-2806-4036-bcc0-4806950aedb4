package model

import (
	"time"

	"github.com/google/uuid"
)

// 请求和响应DTO定义

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required" example:"admin"`
	Password string `json:"password" binding:"required" example:"password"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token        string    `json:"token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	User         UserInfo  `json:"user"`
}

// UserInfo 用户信息
type UserInfo struct {
	ID          uuid.UUID `json:"id"`
	Username    string    `json:"username"`
	Email       string    `json:"email"`
	DisplayName string    `json:"display_name"`
	Avatar      string    `json:"avatar"`
	Role        UserRole  `json:"role"`
	Status      UserStatus `json:"status"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username    string   `json:"username" binding:"required,min=3,max=50"`
	Email       string   `json:"email" binding:"required,email"`
	Password    string   `json:"password" binding:"required,min=6"`
	DisplayName string   `json:"display_name" binding:"max=100"`
	Phone       string   `json:"phone" binding:"max=20"`
	Role        UserRole `json:"role" binding:"required"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	DisplayName string     `json:"display_name" binding:"max=100"`
	Phone       string     `json:"phone" binding:"max=20"`
	Role        UserRole   `json:"role"`
	Status      UserStatus `json:"status"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"`
}

// CreateCloudAccountRequest 创建云账号请求
type CreateCloudAccountRequest struct {
	Name            string        `json:"name" binding:"required,max=100"`
	Provider        CloudProvider `json:"provider" binding:"required"`
	AccessKeyID     string        `json:"access_key_id" binding:"required"`
	AccessKeySecret string        `json:"access_key_secret" binding:"required"`
	Region          string        `json:"region" binding:"required"`
	Description     string        `json:"description" binding:"max=500"`
}

// UpdateCloudAccountRequest 更新云账号请求
type UpdateCloudAccountRequest struct {
	Name            string `json:"name" binding:"max=100"`
	AccessKeyID     string `json:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret"`
	Region          string `json:"region"`
	Description     string `json:"description" binding:"max=500"`
	Status          string `json:"status"`
}

// ResourceQuery 资源查询参数
type ResourceQuery struct {
	Provider       CloudProvider  `form:"provider"`
	Type           ResourceType   `form:"type"`
	Status         ResourceStatus `form:"status"`
	Region         string         `form:"region"`
	Zone           string         `form:"zone"`
	CloudAccountID uuid.UUID      `form:"cloud_account_id"`
	Name           string         `form:"name"`
	Tags           string         `form:"tags"`
	Page           int            `form:"page,default=1"`
	PageSize       int            `form:"page_size,default=20"`
	SortBy         string         `form:"sort_by,default=created_at"`
	SortOrder      string         `form:"sort_order,default=desc"`
}

// CreateResourceRequest 创建资源请求
type CreateResourceRequest struct {
	CloudAccountID uuid.UUID      `json:"cloud_account_id" binding:"required"`
	Type           ResourceType   `json:"type" binding:"required"`
	Name           string         `json:"name" binding:"required,max=255"`
	CloudID        string         `json:"cloud_id" binding:"required"`
	Region         string         `json:"region" binding:"required"`
	Zone           string         `json:"zone"`
	Status         ResourceStatus `json:"status" binding:"required"`
	Tags           map[string]interface{} `json:"tags"`
	Properties     map[string]interface{} `json:"properties"`
	Metadata       map[string]interface{} `json:"metadata"`
}

// UpdateResourceRequest 更新资源请求
type UpdateResourceRequest struct {
	Name       string                 `json:"name" binding:"max=255"`
	Status     ResourceStatus         `json:"status"`
	Tags       map[string]interface{} `json:"tags"`
	Properties map[string]interface{} `json:"properties"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// CreateConfigItemRequest 创建配置项请求
type CreateConfigItemRequest struct {
	ResourceID  uuid.UUID `json:"resource_id" binding:"required"`
	Key         string    `json:"key" binding:"required,max=255"`
	Value       string    `json:"value"`
	Type        string    `json:"type" binding:"required,max=50"`
	Description string    `json:"description" binding:"max=500"`
	IsSecret    bool      `json:"is_secret"`
	Tags        map[string]interface{} `json:"tags"`
}

// UpdateConfigItemRequest 更新配置项请求
type UpdateConfigItemRequest struct {
	Value       string `json:"value"`
	Description string `json:"description" binding:"max=500"`
	IsSecret    bool   `json:"is_secret"`
	Tags        map[string]interface{} `json:"tags"`
}

// CreateResourceRelationRequest 创建资源关系请求
type CreateResourceRelationRequest struct {
	SourceResourceID uuid.UUID    `json:"source_resource_id" binding:"required"`
	TargetResourceID uuid.UUID    `json:"target_resource_id" binding:"required"`
	Type             RelationType `json:"type" binding:"required"`
	Description      string       `json:"description" binding:"max=500"`
	Properties       map[string]interface{} `json:"properties"`
}

// SyncTaskRequest 同步任务请求
type SyncTaskRequest struct {
	CloudAccountID uuid.UUID `json:"cloud_account_id" binding:"required"`
	Type           string    `json:"type" binding:"required"` // full, incremental
	ResourceTypes  []ResourceType `json:"resource_types"`
}

// AlertQuery 告警查询参数
type AlertQuery struct {
	ResourceID uuid.UUID   `form:"resource_id"`
	Level      AlertLevel  `form:"level"`
	Status     AlertStatus `form:"status"`
	Source     string      `form:"source"`
	StartTime  time.Time   `form:"start_time"`
	EndTime    time.Time   `form:"end_time"`
	Page       int         `form:"page,default=1"`
	PageSize   int         `form:"page_size,default=20"`
}

// CreateAlertRequest 创建告警请求
type CreateAlertRequest struct {
	ResourceID  uuid.UUID  `json:"resource_id"`
	Title       string     `json:"title" binding:"required,max=255"`
	Description string     `json:"description"`
	Level       AlertLevel `json:"level" binding:"required"`
	Source      string     `json:"source" binding:"required,max=100"`
	RuleID      string     `json:"rule_id" binding:"max=255"`
	Tags        map[string]interface{} `json:"tags"`
}

// PaginationResponse 分页响应
type PaginationResponse struct {
	Page      int   `json:"page"`
	PageSize  int   `json:"page_size"`
	Total     int64 `json:"total"`
	TotalPage int   `json:"total_page"`
}

// ListResponse 列表响应
type ListResponse struct {
	Data       interface{}        `json:"data"`
	Pagination PaginationResponse `json:"pagination"`
}

// APIResponse 通用API响应
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// HealthResponse 健康检查响应
type HealthResponse struct {
	Status    string            `json:"status"`
	Timestamp time.Time         `json:"timestamp"`
	Services  map[string]string `json:"services"`
}

// StatsResponse 统计响应
type StatsResponse struct {
	TotalResources   int64                    `json:"total_resources"`
	ResourcesByType  map[ResourceType]int64   `json:"resources_by_type"`
	ResourcesByCloud map[CloudProvider]int64  `json:"resources_by_cloud"`
	ResourcesByStatus map[ResourceStatus]int64 `json:"resources_by_status"`
	AlertsCount      int64                    `json:"alerts_count"`
	ActiveAlerts     int64                    `json:"active_alerts"`
}
