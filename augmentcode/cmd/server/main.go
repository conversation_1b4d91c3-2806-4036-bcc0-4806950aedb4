package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"cmdb-platform/internal/api"
	"cmdb-platform/internal/config"
	"cmdb-platform/internal/repository"
	"cmdb-platform/internal/service"
	"cmdb-platform/pkg/cloud"
	"cmdb-platform/pkg/cloud/aliyun"
	"cmdb-platform/pkg/cloud/aws"
	"cmdb-platform/pkg/cloud/tencent"
	"cmdb-platform/pkg/logger"
	
	"cmdb-platform/internal/model"
)

var (
	configPath = flag.String("config", "configs/config.yaml", "配置文件路径")
	version    = "1.0.0"
	buildTime  = "unknown"
	gitCommit  = "unknown"
)

func main() {
	flag.Parse()
	
	fmt.Printf("CMDB Platform v%s (build: %s, commit: %s)\n", version, buildTime, gitCommit)
	
	// 加载配置
	cfg, err := config.Load(*configPath)
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}
	
	// 初始化日志
	if err := logger.Init(&cfg.Log); err != nil {
		fmt.Printf("Failed to init logger: %v\n", err)
		os.Exit(1)
	}
	
	logger.Infof("Starting CMDB Platform v%s", version)
	logger.Infof("Config loaded from: %s", *configPath)
	
	// 初始化数据库
	db, err := repository.NewDatabase(cfg)
	if err != nil {
		logger.Fatalf("Failed to connect database: %v", err)
	}
	defer db.Close()
	
	// 创建索引
	if err := db.CreateIndexes(); err != nil {
		logger.Warnf("Failed to create indexes: %v", err)
	}
	
	// 初始化默认数据
	if err := db.InitDefaultData(); err != nil {
		logger.Warnf("Failed to init default data: %v", err)
	}
	
	// 初始化仓库层
	resourceRepo := repository.NewResourceRepository(db.GetDB())
	cloudAccountRepo := repository.NewCloudAccountRepository(db.GetDB())
	syncTaskRepo := repository.NewSyncTaskRepository(db.GetDB())
	
	// 初始化云服务管理器
	cloudManager := cloud.NewManager()
	
	// 注册云服务商
	if err := registerCloudProviders(cloudManager, cfg); err != nil {
		logger.Fatalf("Failed to register cloud providers: %v", err)
	}
	
	// 初始化服务层
	resourceService := service.NewResourceService(
		resourceRepo,
		cloudAccountRepo,
		syncTaskRepo,
		cloudManager,
	)
	
	// 初始化API路由
	router := api.NewRouter(cfg, resourceService)
	router.SetupRoutes()
	
	// 创建HTTP服务器
	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler:      router.GetEngine(),
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
	}
	
	// 启动服务器
	go func() {
		logger.Infof("Server starting on %s", server.Addr)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("Failed to start server: %v", err)
		}
	}()
	
	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	logger.Info("Shutting down server...")
	
	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := server.Shutdown(ctx); err != nil {
		logger.Errorf("Server forced to shutdown: %v", err)
	}
	
	logger.Info("Server exited")
}

// registerCloudProviders 注册云服务商
func registerCloudProviders(manager *cloud.Manager, cfg *config.Config) error {
	// 注册阿里云
	if cfg.CloudProviders.Aliyun.Enabled {
		aliyunClient, err := aliyun.NewClient(
			cfg.CloudProviders.Aliyun.AccessKeyID,
			cfg.CloudProviders.Aliyun.AccessKeySecret,
			cfg.CloudProviders.Aliyun.Region,
		)
		if err != nil {
			return fmt.Errorf("failed to create aliyun client: %w", err)
		}
		manager.RegisterProvider(model.CloudProviderAliyun, aliyunClient)
		logger.Info("Aliyun provider registered")
	}
	
	// 注册腾讯云
	if cfg.CloudProviders.Tencent.Enabled {
		tencentClient, err := tencent.NewClient(
			cfg.CloudProviders.Tencent.SecretID,
			cfg.CloudProviders.Tencent.SecretKey,
			cfg.CloudProviders.Tencent.Region,
		)
		if err != nil {
			return fmt.Errorf("failed to create tencent client: %w", err)
		}
		manager.RegisterProvider(model.CloudProviderTencent, tencentClient)
		logger.Info("Tencent provider registered")
	}
	
	// 注册AWS
	if cfg.CloudProviders.AWS.Enabled {
		awsClient, err := aws.NewClient(
			cfg.CloudProviders.AWS.AccessKeyID,
			cfg.CloudProviders.AWS.SecretAccessKey,
			cfg.CloudProviders.AWS.Region,
		)
		if err != nil {
			return fmt.Errorf("failed to create aws client: %w", err)
		}
		manager.RegisterProvider(model.CloudProviderAWS, awsClient)
		logger.Info("AWS provider registered")
	}
	
	return nil
}
